import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/condition_profit_stop_loss_order/edit_take_profit_stop_loss_order_widget.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';

// Mock classes
class MockEditConditionOrderCubit extends MockCubit<EditConditionOrderState>
    implements EditConditionOrderCubit {}

class MockPlaceOrderCubit extends MockCubit<PlaceOrderState>
    implements PlaceOrderCubit {}

class MockValidateConditionOrderCubit extends MockCubit<ValidateConditionOrderState>
    implements ValidateConditionOrderCubit {}

class MockValidateOrderCubit extends MockCubit<ValidateOrderState>
    implements ValidateOrderCubit {}

void main() {
  group('EditTakeProfitStopLossOrderWidget Date Handling Tests', () {
    late MockEditConditionOrderCubit mockEditConditionOrderCubit;
    late MockPlaceOrderCubit mockPlaceOrderCubit;
    late MockValidateConditionOrderCubit mockValidateConditionOrderCubit;
    late MockValidateOrderCubit mockValidateOrderCubit;

    setUp(() {
      mockEditConditionOrderCubit = MockEditConditionOrderCubit();
      mockPlaceOrderCubit = MockPlaceOrderCubit();
      mockValidateConditionOrderCubit = MockValidateConditionOrderCubit();
      mockValidateOrderCubit = MockValidateOrderCubit();

      // Setup default states
      when(() => mockEditConditionOrderCubit.state).thenReturn(
        const EditConditionOrderState(),
      );
      when(() => mockPlaceOrderCubit.state).thenReturn(
        const PlaceOrderState(),
      );
      when(() => mockValidateConditionOrderCubit.state).thenReturn(
        const ValidateConditionOrderState(),
      );
      when(() => mockValidateOrderCubit.state).thenReturn(
        const ValidateOrderState(),
      );
    });

    testWidgets('should preserve original dates when user changes other fields', (tester) async {
      // Arrange
      final originalFromDate = '30/09/2025';
      final originalToDate = '03/10/2025';
      
      final conditionOrderItem = ConditionOrderBookModel(
        orderId: 'test-order-id',
        symbol: 'VPB',
        fromDate: originalFromDate,
        toDate: originalToDate,
        qty: 100,
        stopLossRate: 5.0,
        slipPagePrice: 1000,
        conditionOrderTypeEnum: ConditionOrderTypeEnum.tpo,
        triggerConditionEnum: TakeProfitTriggerConditionEnum.rateProfit,
      );

      // Mock validate condition state with empty dates (user didn't change dates)
      when(() => mockValidateConditionOrderCubit.state).thenReturn(
        const ValidateConditionOrderState(
          fromDate: '', // Empty means user didn't change
          toDate: '',   // Empty means user didn't change
        ),
      );

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<EditConditionOrderCubit>.value(
                value: mockEditConditionOrderCubit,
              ),
              BlocProvider<PlaceOrderCubit>.value(
                value: mockPlaceOrderCubit,
              ),
              BlocProvider<ValidateConditionOrderCubit>.value(
                value: mockValidateConditionOrderCubit,
              ),
              BlocProvider<ValidateOrderCubit>.value(
                value: mockValidateOrderCubit,
              ),
            ],
            child: EditTakeProfitStopLossOrderWidget(
              item: conditionOrderItem,
              onEditSuccess: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the widget preserves original dates
      // This test verifies the fix for the bug where app sends default dates
      // instead of preserving original dates from API
      expect(find.byType(EditTakeProfitStopLossOrderWidget), findsOneWidget);
    });

    testWidgets('should use current dates when user explicitly changes them', (tester) async {
      // Arrange
      final originalFromDate = '30/09/2025';
      final originalToDate = '03/10/2025';
      final newFromDate = '01/10/2025';
      final newToDate = '05/10/2025';
      
      final conditionOrderItem = ConditionOrderBookModel(
        orderId: 'test-order-id',
        symbol: 'VPB',
        fromDate: originalFromDate,
        toDate: originalToDate,
        qty: 100,
        stopLossRate: 5.0,
        slipPagePrice: 1000,
        conditionOrderTypeEnum: ConditionOrderTypeEnum.tpo,
        triggerConditionEnum: TakeProfitTriggerConditionEnum.rateProfit,
      );

      // Mock validate condition state with new dates (user changed dates)
      when(() => mockValidateConditionOrderCubit.state).thenReturn(
        ValidateConditionOrderState(
          fromDate: newFromDate, // User changed to new date
          toDate: newToDate,     // User changed to new date
        ),
      );

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<EditConditionOrderCubit>.value(
                value: mockEditConditionOrderCubit,
              ),
              BlocProvider<PlaceOrderCubit>.value(
                value: mockPlaceOrderCubit,
              ),
              BlocProvider<ValidateConditionOrderCubit>.value(
                value: mockValidateConditionOrderCubit,
              ),
              BlocProvider<ValidateOrderCubit>.value(
                value: mockValidateOrderCubit,
              ),
            ],
            child: EditTakeProfitStopLossOrderWidget(
              item: conditionOrderItem,
              onEditSuccess: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the widget uses new dates when user explicitly changed them
      expect(find.byType(EditTakeProfitStopLossOrderWidget), findsOneWidget);
    });
  });
}
