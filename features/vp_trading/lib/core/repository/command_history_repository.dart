import 'package:vp_common/error/handle_error.dart';
import 'package:vp_core/base/base_response/base_paging_response.dart';
import 'package:vp_core/base/base_response/base_response.dart';
import 'package:vp_trading/core/service/command_history_service.dart';
import 'package:vp_trading/model/order/command_history/command_history_model.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/condition_order_book/sub_condition_order_model.dart';
import 'package:vp_trading/model/order/gtc_old/app_auth_obj.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/order/request/cancel_condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/model/request/order/update_order_request.dart';

abstract class CommandHistoryRepository {
  Future<BaseResponse<BasePagingResponse<OrderBookModel>>> getOrder({
    required OrderBookRequest queries,
  });
  Future<BaseResponse<BasePagingResponse<OrderCommandHistoryModel>>>
  getOrderHist(OrderBookRequest queries);

  Future<BaseResponse> deleteOrder(DeleteOrderRequest body);

  Future<BaseResponse> editOrder(UpdateOrderRequest body);

  Future<BaseResponse> deleteAllOrder(DeleteOrderRequest body);

  Future<BaseResponse> deleteBatchOrder(DeleteOrderRequest body);

  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getOrderCondition(OrderBookRequest queries);

  Future<BaseResponse> deleteConditionOrder(DeleteOrderRequest body);

  Future<BaseResponse> editConditonOrder(ConditionOrderRequestModel request);

  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getFuConditionOrderBook(OrderBookRequest queries);

  Future<BaseResponse<BasePagingResponse<SubConditionOrderModel>>>
  getFuSubConditionOrderBook(String accountId, String orderId);

  Future<BaseResponse> deleteFuConditionOrder(
    DeleteFuConditionOrderRequest body,
  );
  Future<BaseResponse> deleteAllFuConditionOrder(
    DeleteFuConditionOrderRequest body,
  );

  Future<BaseResponse> editFUConditonOrder(ConditionOrderRequestModel request);

  // Huỷ lệnh gtc
  Future<BaseResponse> postInitVerifyTransaction(
    CancelConditionOrderRequestModel request,
  );
  // Huỷ lệnh gtc
  Future<BaseResponse<AppAuthObj>> initVerifyTransaction(String accountId);
}

class CommandHistoryRepositoryImpl implements CommandHistoryRepository {
  final CommandHistoryService commandHistoryService;

  CommandHistoryRepositoryImpl({required this.commandHistoryService});

  @override
  Future<BaseResponse<BasePagingResponse<OrderBookModel>>> getOrder({
    required OrderBookRequest queries,
  }) async {
    try {
      return await commandHistoryService.getOrder(queries);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<BasePagingResponse<OrderCommandHistoryModel>>>
  getOrderHist(OrderBookRequest queries) async {
    try {
      return await commandHistoryService.getOrderHist(queries);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> deleteOrder(DeleteOrderRequest body) async {
    try {
      return await commandHistoryService.deleteOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> editOrder(UpdateOrderRequest body) async {
    try {
      return await commandHistoryService.editOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> deleteAllOrder(DeleteOrderRequest body) async {
    try {
      return await commandHistoryService.deleteAllOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> deleteBatchOrder(DeleteOrderRequest body) async {
    try {
      return await commandHistoryService.deleteBatchOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getOrderCondition(OrderBookRequest queries) async {
    try {
      return await commandHistoryService.getConditionOrderBook(queries);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> deleteConditionOrder(DeleteOrderRequest body) async {
    try {
      return await commandHistoryService.deleteConditionOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> editConditonOrder(
    ConditionOrderRequestModel request,
  ) async {
    try {
      return await commandHistoryService.editConditonOrder(request);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getFuConditionOrderBook(OrderBookRequest queries) async {
    try {
      return await commandHistoryService.getFuConditionOrderBook(queries);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<BasePagingResponse<SubConditionOrderModel>>>
  getFuSubConditionOrderBook(String accountId, String orderId) async {
    try {
      return await commandHistoryService.getFuSubConditionOrderBook(
        accountId,
        orderId,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> deleteAllFuConditionOrder(
    DeleteFuConditionOrderRequest body,
  ) async {
    try {
      return await commandHistoryService.deleteAllFuConditionOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> deleteFuConditionOrder(
    DeleteFuConditionOrderRequest body,
  ) async {
    try {
      return await commandHistoryService.deleteFuConditionOrder(body);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> editFUConditonOrder(
    ConditionOrderRequestModel request,
  ) async {
    try {
      return await commandHistoryService.editFUConditonOrder(request);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> postInitVerifyTransaction(
    CancelConditionOrderRequestModel request,
  ) async {
    try {
      return await commandHistoryService.postInitVerifyTransaction(
        request.accountId ?? '',
        request,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<AppAuthObj>> initVerifyTransaction(
    String accountId,
  ) async {
    try {
      return await commandHistoryService.initVerifyTransaction(accountId);
    } catch (e) {
      throw HandleError.from(e);
    }
  }
}
