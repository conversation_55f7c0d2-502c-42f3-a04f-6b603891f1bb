import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/place_order_path_api.dart';
import 'package:vp_trading/model/order/command_history/command_history_model.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/condition_order_book/sub_condition_order_model.dart';
import 'package:vp_trading/model/order/gtc_old/app_auth_obj.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/order/request/cancel_condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/model/request/order/update_order_request.dart';

part 'command_history_service.g.dart';

@RestApi()
abstract class CommandHistoryService {
  factory CommandHistoryService(Dio dio, {String baseUrl}) =
      _CommandHistoryService;

  @GET("/neo-inv-order/public/v1/trading/orderBook")
  Future<BaseResponse<BasePagingResponse<OrderBookModel>>> getOrder(
    @Queries() OrderBookRequest queries,
  );

  @GET("/neo-inv-order/public/v1/trading/orderHistory")
  Future<BaseResponse<BasePagingResponse<OrderCommandHistoryModel>>>
  getOrderHist(@Queries() OrderBookRequest queries);

  @DELETE("/neo-inv-order/public/v1/trading/orders")
  Future<BaseResponse> deleteOrder(@Body() DeleteOrderRequest body);

  @PUT("/neo-inv-order/public/v1/trading/orders")
  Future<BaseResponse> editOrder(@Body() UpdateOrderRequest body);

  @DELETE("/neo-inv-order/public/v1/trading/orders/all")
  Future<BaseResponse> deleteAllOrder(@Body() DeleteOrderRequest body);

  @DELETE("/neo-inv-order/public/v1/trading/orders/batch")
  Future<BaseResponse> deleteBatchOrder(@Body() DeleteOrderRequest body);

  @GET("/neo-inv-order/public/v1/trading/stockConditionOrderBook")
  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getConditionOrderBook(@Queries() OrderBookRequest queries);

  @DELETE("/neo-inv-order/public/v1/trading/stockConditionOrder")
  Future<BaseResponse> deleteConditionOrder(@Body() DeleteOrderRequest body);

  @PUT("/neo-inv-order/public/v1/trading/stockConditionOrder")
  Future<BaseResponse> editConditonOrder(
    @Body() ConditionOrderRequestModel request,
  );

  @GET(PlaceOrderPathApi.fuConditionOrderBook)
  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getFuConditionOrderBook(@Queries() OrderBookRequest queries);

  @GET("/neo-inv-order/public/v1/trading/fuSubConditionOrder")
  Future<BaseResponse<BasePagingResponse<SubConditionOrderModel>>>
  getFuSubConditionOrderBook(
    @Query('accountId') String accountId,
    @Query('orderId') String orderId,
  );

  @DELETE(PlaceOrderPathApi.tradingDerivativeConditionOrder)
  Future<BaseResponse> deleteFuConditionOrder(
    @Body() DeleteFuConditionOrderRequest body,
  );

  @DELETE("/neo-inv-order/public/v1/trading/fuConditionOrder/cancelAll")
  Future<BaseResponse> deleteAllFuConditionOrder(
    @Body() DeleteFuConditionOrderRequest body,
  );

  @PUT(PlaceOrderPathApi.tradingDerivativeConditionOrder)
  Future<BaseResponse> editFUConditonOrder(
    @Body() ConditionOrderRequestModel request,
  );

  // Huỷ lệnh gtc
  @POST(
    "https://external-uat-krx.vpbanks.com.vn/flex/accounts/{accountId}/cancelConditionOrder",
  )
  Future<BaseResponse> postInitVerifyTransaction(
    @Path('accountId') String accountId,
    @Body() CancelConditionOrderRequestModel request,
  );

  @POST(
    "https://external-uat-krx.vpbanks.com.vn/flex/accounts/{accountId}/initVerifyTransaction",
  )
  Future<BaseResponse<AppAuthObj>> initVerifyTransaction(
    @Path('accountId') String accountId,
  );
}
