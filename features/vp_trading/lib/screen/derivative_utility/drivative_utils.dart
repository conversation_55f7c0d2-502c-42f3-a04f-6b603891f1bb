import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/vp_trading_navigator.dart';

class DerivativeUtils {
  static bool checkDerivativeAccount(BuildContext context) {
    final accountCubit = GetIt.instance.get<SubAccountCubit>();

    if (!accountCubit.anyDerivativeAccount) {
      tradingNavigator.showWarningDontHaveDerivativeAccount(
        context,
        onRegister: () {
          context.pop();
          tradingNavigator.openDerivativeRegisterPage(context);
        },
      );
      return false;
    }

    if (accountCubit.isDerivativePending) {
      tradingNavigator.showDerivativeAccountPendingApprovalDialog(context);
      return false;
    }

    if (accountCubit.isInvalidAccount) {
      tradingNavigator.showInvalidAccountStatusDialog(context);
      return false;
    }

    return true;
  }
}
