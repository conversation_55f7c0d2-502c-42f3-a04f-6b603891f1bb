import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_state.dart';
import 'package:vp_trading/cubit/order_container/normal_order/normal_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/router/trading_router.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_list.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_title_widget.dart';
import 'package:vp_trading/screen/order_container/widget/dialog_cancel_all_order.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class PlaceOrderContainer extends StatefulWidget {
  const PlaceOrderContainer({super.key, required this.subAccountType});

  final SubAccountType subAccountType;

  @override
  State<PlaceOrderContainer> createState() => _PlaceOrderContainerState();
}

class _PlaceOrderContainerState extends State<PlaceOrderContainer> {
  bool _isOrderListVisible = true;

  void _toggleOrderList() {
    setState(() {
      _isOrderListVisible = !_isOrderListVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) =>
                  NormalOrderCubit()
                    ..initForWaitingOrders(widget.subAccountType),
        ),
        BlocProvider(create: (context) => DeleteUpdateOrderCubit()),
      ],
      child: ColoredBox(
        color: vpColor.backgroundElevation0,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _header(),
              const SizedBox(height: 8),
              const DividerWidget(),
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                child:
                    _isOrderListVisible
                        ? _buildNormalOrderList()
                        : const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Row _header() {
    return Row(
      children: [
        BlocBuilder<NormalOrderCubit, NormalOrderState>(
          builder: (context, state) {
            return GestureDetector(
              onTap: _toggleOrderList,
              child: Text(
                "${VPTradingLocalize.current.trading_pending_order} (${state.listItems.length})",
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
            );
          },
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: _toggleOrderList,
          child: AnimatedRotation(
            turns: _isOrderListVisible ? 0.5 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: DesignAssets.icons.dropdown.icArrowBottom.svg(
              colorFilter: ColorFilter.mode(
                vpColor.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
        const Spacer(),
        Row(
          children: [
            GestureDetector(
              onTap: () {
                context.push(TradingRouter.normalOderBookScreen.routeName);
              },
              child: Text(
                VPTradingLocalize.current.trading_command_history,
                style: vpTextStyle.captionMedium.copyColor(vpColor.textBrand),
              ),
            ),
          ],
        ),
      ],
    );
  }

  BlocBuilder<NormalOrderCubit, NormalOrderState> _buildNormalOrderList() {
    return BlocBuilder<NormalOrderCubit, NormalOrderState>(
      builder: (context, state) {
        return Column(
          children: [
            if (state.isLoading) const CommandHistoryLoadingWidget(),
            if (!state.isLoading && state.listItems.isEmpty)
              NoDataView(
                content: VPTradingLocalize.current.trading_no_data_message,
              ),
            if (state.listItems.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buttomDeleteAll(context),

              const SizedBox(height: 16),
              NormalOrderList(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                items: state.listItems,
                hasMore: false,
                loadMore: () async {},
                refresh: () async {
                  context.read<NormalOrderCubit>().initForWaitingOrders(
                    widget.subAccountType,
                  );
                },
                editSuccess:
                    () async => context
                        .read<NormalOrderCubit>()
                        .initForWaitingOrders(widget.subAccountType),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buttomDeleteAll(BuildContext context) {
    return BlocListener<DeleteUpdateOrderCubit, DeleteUpdateOrderState>(
      listener: (context, state) {
        if (state.status == DeleteUpdateOrderStateEnum.isDeleteAllSuccess) {
          context.showSuccess(
            content: VPTradingLocalize.current.trading_cancel_order_success,
          );
          context.read<NormalOrderCubit>().initForWaitingOrders(
            widget.subAccountType,
          );
        }
        if (state.status == DeleteUpdateOrderStateEnum.isDeleteSuccess) {
          context.showSnackBar(
            content: VPTradingLocalize.current.trading_cancel_order_success,
            snackBarType: VPSnackBarType.success,
          );

          context.read<NormalOrderCubit>().initForWaitingOrders(
            widget.subAccountType,
          );
        }
        if (state.errorMessage != null) {
          context.showError(content: state.errorMessage ?? "-");
          context.read<DeleteUpdateOrderCubit>().resetErrorMessage();
        }
      },
      child: ConditionNormalTitle(
        expandTitleWidget: const [10, 6, 7, 12],
        showTitleDeleteAll: true,
        onDeleteAll: () {
          dialogConfirmDeleteAllOrder(context, () {
            context.read<DeleteUpdateOrderCubit>().deleteAllOrder(
              DeleteOrderRequest(
                accountId: widget.subAccountType.toSubAccountModel()?.id ?? "",
                requestId: "app_${AppHelper().genXRequestID()}",
                via: "V",
                orderId: context
                    .read<NormalOrderCubit>()
                    .state
                    .listOrderIdCancel
                    .join(','),
              ),
            );
            Navigator.of(context).pop();
          });
        },
      ),
    );
  }
}
