import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/condition_profit_stop_loss_order/condition_profit_stop_loss_order.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/condition_pending_order.dart';
import 'package:vp_trading/screen/order_container/conditional_order/widget/condition_detail_gtc_item.dart';
import 'package:vp_trading/screen/order_container/conditional_order/widget/condition_detail_seo_item.dart';
import 'package:vp_trading/screen/order_container/conditional_order/widget/condition_detail_tpo_slo_item.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_item_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';

import '../../../../widgets/content_expansion_widget.dart';

class ConditionDetailBottomSheet extends StatefulWidget {
  const ConditionDetailBottomSheet({
    super.key,
    required this.model,
    this.onEditCommandSucces,
    this.onDeleteCommand,
  });

  final ConditionOrderBookModel model;

  final VoidCallback? onEditCommandSucces;

  final VoidCallback? onDeleteCommand;

  @override
  State<ConditionDetailBottomSheet> createState() =>
      _ConditionDetailBottomSheetState();
}

class _ConditionDetailBottomSheetState
    extends State<ConditionDetailBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final titleStyle = vpTextStyle.subtitle14.copyColor(themeData.gray700);

    final valueStyle = vpTextStyle.subtitle14.copyColor(themeData.focus);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDetailItem(context),

          ///Trạng thái
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle?.copyWith(
              color: widget.model.orderViewStatusEnum.textColor,
            ),
            title: VPTradingLocalize.current.trading_status,
            value: widget.model.orderViewStatusEnum.title,
          ),
          const SizedBox(height: 8),

          /// build bottom actions view
          buildActionBottom(context),
        ],
      ),
    );
  }

  Widget buildActionBottom(BuildContext context) {
    if (widget.model.allowAmend == StockAppConstants.n &&
        widget.model.allowCancel == StockAppConstants.n) {
      return Padding(
        padding: const EdgeInsets.only(top: 8),
        child: VpsButton.secondaryXsSmall(
          title: VPCommonLocalize.current.close,
          onPressed: () {
            Navigator.of(context).pop();
          },
          alignment: Alignment.center,
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          if (widget.model.allowCancel == StockAppConstants.y)
            Expanded(
              child: VpsButton.secondaryDangerXsSmall(
                title: VPTradingLocalize.current.trading_cancel_order,
                onPressed: () {
                  dialogConfirmDeleteOrder(context, () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                    if (widget.onDeleteCommand != null) {
                      widget.onDeleteCommand!();
                    }
                  });
                },
                alignment: Alignment.center,
              ),
            ),
          if (widget.model.allowAmend == StockAppConstants.y) ...[
            const SizedBox(width: 8),
            Expanded(
              child: VpsButton.secondaryXsSmall(
                title: VPTradingLocalize.current.trading_edit_order,
                onPressed: () {
                  _showEditOrder(context);
                },
                alignment: Alignment.center,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showEditOrder(BuildContext context) {
    if (widget.model.conditionOrderTypeEnum == ConditionOrderTypeEnum.seo) {
      opendEditConditionPendingOrderBottomSheet(
        context: context,
        item: widget.model,
        onEditSuccess: () {
          context.pop();
          context.pop();
          context.showSuccess(content: 'Đã gửi thành công');
          widget.onEditCommandSucces?.call();
        },
      );
    } else {
      opendEditConditionProfitStopLossOrderBottomSheet(
        context: context,
        item: widget.model,
        onEditSuccess: () {
          context.pop();
          context.pop();
          context.showSuccess(content: 'Đã gửi thành công');
          widget.onEditCommandSucces?.call();
        },
      );
    }
  }

  _buildDetailItem(BuildContext context) {
    switch (widget.model.conditionOrderTypeEnum) {
      case ConditionOrderTypeEnum.gtc:
        return ConditionDetailGtcItem(model: widget.model);
      case ConditionOrderTypeEnum.seo:
        return ConditionDetailSeoItem(model: widget.model);
      case ConditionOrderTypeEnum.tpo:
      case ConditionOrderTypeEnum.slo:
        return ConditionDetailTpoSloItem(model: widget.model);
      default:
        return const SizedBox.shrink();
    }
  }
}

void dialogConfirmDeleteOrder(
  BuildContext context,
  VoidCallback onDeleteCommand,
) async {
  VPPopup.outlineAndPrimaryButton(
        title: VPTradingLocalize.current.trading_cancel_order_title,
        content: VPTradingLocalize.current.trading_cancel_order_confirm_message,
      )
      .copyWith(icon: VpTradingAssets.icons.orderCancelIcon.svg())
      .copyWith(
        button: VpsButton.secondarySmall(
          title: VPTradingLocalize.current.trading_close,
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerSmall(
          title: VPTradingLocalize.current.trading_cancel_order_title,
          onPressed: () {
            onDeleteCommand();
          },
        ),
      )
      .showDialog(context);
}
