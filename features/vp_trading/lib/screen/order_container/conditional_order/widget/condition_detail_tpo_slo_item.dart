import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/utils/stock_utils.dart';
import 'package:vp_trading/widgets/content_expansion_widget.dart';

class ConditionDetailTpoSloItem extends StatelessWidget {
  const ConditionDetailTpoSloItem({super.key, required this.model});
  final ConditionOrderBookModel model;

  @override
  Widget build(BuildContext context) {
    final titleStyle = vpTextStyle.subtitle14.copyColor(themeData.gray700);

    final valueStyle = vpTextStyle.subtitle14.copyColor(themeData.focus);

    final execprice = model.execPrice ?? 0;

    final execqtty = model.execQty ?? 0;

    final qtty = model.qty ?? 0;

    final execqttyFormat = AppNumberFormatUtils.shared.percentFormatter.format(
      execqtty,
    );

    final qttyFormat = AppNumberFormatUtils.shared.percentFormatter.format(
      qtty,
    );
    return Column(
      children: [
        /// Tiểu khoản
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: VPTradingLocalize.current.trading_sub_account,
          value: StockUtils.getSubAccountName(model.accountId),
          // value: (model.producttypename ?? '').replaceAll(".", ''),
        ),

        /// Mã cổ phiếu
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: VPTradingLocalize.current.trading_history_stock_code,
          value: model.symbol ?? '',
        ),

        /// Lệnh
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: VPTradingLocalize.current.trading_stock_type,
          value: model.orderTypeEnum.title,
        ),

        /// Loại lệnh
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: VPTradingLocalize.current.trading_command_type,
          value: model.conditionOrderTypeEnum.title,
        ),

        /// Tỷ lệ cắt lỗ
        if (model.triggerConditionEnum ==
            TakeProfitTriggerConditionEnum.rateProfit)
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title:
                model.conditionOrderTypeEnum == ConditionOrderTypeEnum.slo
                    ? "Tỷ lệ cắt lỗ"
                    : "Tỷ lệ chốt lời",
            value: '${model.stopLossRate ?? 0}%',
          ),

        if (model.triggerConditionEnum ==
            TakeProfitTriggerConditionEnum.slippageProfit)
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title:
                model.conditionOrderTypeEnum == ConditionOrderTypeEnum.slo
                    ? "Biên giá cắt lỗ"
                    : "Biên giá chốt lời",
            value: ((model.stopLossPriceAmp ?? 0) / 1000)
                .toDouble()
                .getPriceFormatted(convertToThousand: false),
          ),

        /// Biên trượt
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: "Biên trượt",
          value: model.slipPagePrice.toString().getPriceFormatted(),
        ),

        /// Giá đặt lệnh
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: VPTradingLocalize.current.trading_order_price,
          value: model.price.toString().getPriceFormatted(),
        ),

        /// Khối lượng khớp
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          value: qttyFormat,
          title: "Khối lượng",
        ),

        /// Thời gian hiệu lực
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          allowTitleWrap: true,
          title: "Khi có sự kiện quyền pha loãng giá cổ phiếu",
          value: model.diluationActionFormat,
        ),

        /// Thời gian đặt lệnh
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: VPTradingLocalize.current.trading_order_time,
          value: model.orderCreatedAt ?? '',
        ),

        /// Thời gian hiệu lực
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: "Thời gian hiệu lực",
          value: "${model.fromDate} - ${model.toDate}",
        ),
      ],
    );
  }
}
