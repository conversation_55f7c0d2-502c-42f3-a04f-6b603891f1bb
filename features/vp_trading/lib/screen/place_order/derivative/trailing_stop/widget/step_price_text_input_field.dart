import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart' show Blink;
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/info_icon_button.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class StepPriceTextInputField extends StatefulWidget {
  final TextEditingController stepPriceController;

  const StepPriceTextInputField({super.key, required this.stepPriceController});

  @override
  State<StepPriceTextInputField> createState() =>
      StepPriceTextInputFieldState();
}

class StepPriceTextInputFieldState extends State<StepPriceTextInputField> {
  final ValueNotifier<bool> _stepPriceBlink = ValueNotifier(false);
  final _stepPriceFocusNode = FocusNode();

  void _focusListener() {
    if (_stepPriceFocusNode.hasFocus) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.stepPrice,
      );
    } else {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _stepPriceFocusNode.addListener(_focusListener);
  }

  @override
  void dispose() {
    _stepPriceFocusNode.dispose();
    _stepPriceBlink.dispose();
    super.dispose();
  }

  void _handleOnPressShowPStepPriceInfo(BuildContext context) {
    _showBottomSheetInfo(
      context,
      VPTradingLocalize.current.stepPriceInfoContentPopup,
    );
  }

  void _showBottomSheetInfo(BuildContext context, String content) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        final bottom = MediaQuery.of(ctx).viewInsets.bottom;
        return Padding(
          padding: EdgeInsets.only(bottom: bottom),
          child: AppBottomSheet(
            isFullSize: false,
            child: Text(
              content,
              style: vpTextStyle.captionSemiBold.copyColor(vpColor.textPrimary),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentStepPrice != current.currentStepPrice,
          listener: (context, state) {
            if (state.currentStepPrice != null) {
              widget.stepPriceController.text = state.currentStepPrice!;
              widget.stepPriceController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.stepPriceController.text.length),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateConditionOrderCubit,
        DerivativeValidateConditionOrderState
      >(
        builder: (context, state) {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Row(
                      children: [
                        Text(
                          VPTradingLocalize.current.derivative_step_price,
                          style: vpTextStyle.body14.copyColor(
                            vpColor.textPrimary,
                          ),
                        ),
                        const SizedBox(width: 4),
                        InfoIconButton(
                          onTap:
                              () => _handleOnPressShowPStepPriceInfo(context),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: Blink(
                        blink: _stepPriceBlink,
                        child: InputFieldBox(
                          isError:
                              state.errorStepPrice.isError &&
                              widget.stepPriceController.text.isNotEmpty,

                          controller: widget.stepPriceController,
                          maxLength: 10,
                          hintText: 'Chênh lệch',
                          onChange: (value) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .onChangeStepPrice(value);
                          },
                          focusNode: _stepPriceFocusNode,
                          scrollPadding: 180,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .stepPriceTap(
                                  text: widget.stepPriceController.text,
                                  increase: increase,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            ...priceDerivativeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateConditionOrderCubit,
                DerivativeValidateConditionOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorStepPrice != current.errorStepPrice ||
                        previous.currentStepPrice != current.currentStepPrice,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorStepPrice.message,
                    text: widget.stepPriceController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
