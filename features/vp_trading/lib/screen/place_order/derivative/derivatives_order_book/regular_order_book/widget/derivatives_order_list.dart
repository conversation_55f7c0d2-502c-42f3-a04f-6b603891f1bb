import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/model/enum/market_type.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/edit_regular_order_book/dialog_edit_derivatives_order.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_item.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/dialog_cancle_derivatives_order.dart';

class DerivativesOrderList extends StatefulWidget {
  final List<OrderBookModel> items;
  final Future<void> Function() refresh;
  final Future<void> Function() editSuccess;
  final bool? shrinkWrap;
  final bool isMultiSelectMode;
  final bool Function(String)? isOrderSelected;
  final ValueChanged<String>? onSelectionChanged;
  final ScrollPhysics? physics;

  const DerivativesOrderList({
    super.key,
    required this.items,
    required this.refresh,
    this.shrinkWrap = false,
    required this.editSuccess,
    this.isMultiSelectMode = false,
    this.isOrderSelected,
    this.onSelectionChanged,
    this.physics,
  });

  @override
  State<DerivativesOrderList> createState() => _DerivativesOrderListState();
}

class _DerivativesOrderListState extends State<DerivativesOrderList> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: widget.physics ?? const AlwaysScrollableScrollPhysics(),
      shrinkWrap: widget.shrinkWrap ?? false,
      itemCount: widget.items.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final item = widget.items[index];
        return DerivativesOrderItem(
          item: item,
          onTap: () {},
          onEdit: () async {
            // Show edit dialog with new function signature
            dialogConfirmEditDerivativesOrders(
              context,
              item,
              onEditSuccess: () async {
                context.pop();
                context.showSuccess(content: 'Đã sửa lệnh thành công');
                await widget.refresh();
              },
              onEditFailure: (message) {
                context.pop();
                context.showError(content: message);
              },
            );
          },
          onCancel: () async {
            dialogConfirmDeleteDerivativesOrders(context, item, () async {
              context.read<DeleteUpdateOrderCubit>().deleteOrder(
                DeleteOrderRequest(
                  orderId: item.orderId ?? '',
                  accountId:
                      GetIt.instance<SubAccountCubit>()
                          .state
                          .derivativeSubAccount
                          .first
                          .id ??
                      '',
                  market: MarketType.derivatives.nameServer,
                  requestId: "app_${AppHelper().genXRequestID()}",
                  via: "V",
                ),
              );
            });
          },
          isMultiSelectMode: widget.isMultiSelectMode,
          isSelected: widget.isOrderSelected?.call(item.orderId ?? '') ?? false,
          onSelectionChanged: widget.onSelectionChanged,
        );
      },
    );
  }
}
