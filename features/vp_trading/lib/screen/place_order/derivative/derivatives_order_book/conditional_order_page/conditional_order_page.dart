import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/cubit/derivatives_condition_order/derivatives_condition_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_state.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/edit_order_conditional_dialog.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/widget/conditional_order_item.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/widget/dialog_cancle_fu_condition_order.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_multi_select_bottom_bar.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_title_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/widget/no_data_derivatives_order_book.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class ConditionalOrderPage extends StatefulWidget {
  const ConditionalOrderPage({super.key});

  @override
  State<ConditionalOrderPage> createState() => _ConditionalOrderPageState();
}

class _ConditionalOrderPageState extends State<ConditionalOrderPage> {
  late DeleteUpdateOrderCubit _deleteUpdateOrderCubit;

  @override
  void initState() {
    super.initState();
    _deleteUpdateOrderCubit = DeleteUpdateOrderCubit();
  }

  @override
  void dispose() {
    _deleteUpdateOrderCubit.close();
    super.dispose();
  }

  void _handleEnterMultiSelectMode() {
    context.read<DerivativesConditionOrderCubit>().toggleMultiSelectMode();
  }

  void _handleExitMultiSelectMode() {
    context.read<DerivativesConditionOrderCubit>().toggleMultiSelectMode();
  }

  Future<void> _handleCancelAllOrders(String listOrderId) async {
    dialogConfirmDeleteMultipleOrders(context, 0, () async {
      _deleteUpdateOrderCubit.deleteAllFUConditionOrder(
        DeleteFuConditionOrderRequest(
          accountId:
              GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
          requestId: "app_${AppHelper().genXRequestID()}",
          via: "V",
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [BlocProvider(create: (context) => _deleteUpdateOrderCubit)],
      child: MultiBlocListener(
        listeners: [
          BlocListener<DeleteUpdateOrderCubit, DeleteUpdateOrderState>(
            listener: (context, deleteState) {
              if (deleteState.status ==
                      DeleteUpdateOrderStateEnum.isDeleteSuccess ||
                  deleteState.status ==
                      DeleteUpdateOrderStateEnum.isDeleteAllSuccess) {
                context.showSuccess(
                  content:
                      VPTradingLocalize.current.trading_cancel_order_success,
                );
                context.read<DerivativesConditionOrderCubit>().loadData();
              } else if (deleteState.status ==
                  DeleteUpdateOrderStateEnum.failure) {
                context.showError(content: deleteState.errorMessage ?? "-");
              }
            },
          ),
        ],
        child: BlocBuilder<
          DerivativesConditionOrderCubit,
          DerivativesConditionOrderState
        >(
          builder: (context, state) {
            return Scaffold(
              body: Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Column(
                  children: [
                    if (state.isLoading)
                      const Expanded(child: CommandHistoryLoadingWidget()),
                    if (!state.isLoading && state.listItems.isEmpty)
                      Expanded(
                        child: PullToRefreshView(
                          onRefresh: () async {
                            await context
                                .read<DerivativesConditionOrderCubit>()
                                .loadData();
                          },
                          child: const NoDataDerivativesOrderBook(),
                        ),
                      ),
                    if (state.listItems.isNotEmpty && !state.isLoading) ...[
                      DerivativesOrderTitleWidget(
                        expandTitleWidget: const [10, 6, 7, 12],
                        showTitleDeleteAll: true,
                        onDeleteAll:
                            () => _handleCancelAllOrders(
                              state.listOrderIdCancel.join(','),
                            ),
                        onMultiSelectMode: _handleEnterMultiSelectMode,
                        isEnableCancelButton: state.isEnableCancelButton,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: PullToRefreshView(
                          onRefresh: () async {
                            await context
                                .read<DerivativesConditionOrderCubit>()
                                .loadData();
                          },
                          child: _buildOrderList(state),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              bottomNavigationBar:
                  state.isMultiSelectMode
                      ? DerivativesOrderMultiSelectBottomBar(
                        selectedCount: state.selectedItems.length,
                        onBack: _handleExitMultiSelectMode,
                        onCancelOrders:
                            () => _deleteOrder(
                              state.selectedItems
                                  .map((item) => item.orderId ?? '')
                                  .where((id) => id.isNotEmpty)
                                  .toList(),
                            ),
                        isLoading: false,
                      )
                      : null,
            );
          },
        ),
      ),
    );
  }

  Widget _buildOrderList(DerivativesConditionOrderState state) {
    return ListView.builder(
      itemCount: state.listItems.length,
      itemBuilder: (context, index) {
        final item = state.listItems[index];
        return ConditionalOrderItem(
          key: ValueKey(item.orderId),
          item: item,
          isSelected: state.selectedItems.contains(item),
          isMultiSelectMode: state.isMultiSelectMode,
          onEdit: () {
            // TODO: Implement edit functionality
            showEditConditionalOrderDialog(context: context, model: item);
          },
          onDelete:
              () => dialogConfirmDeleteFuConditionOrder(
                context,
                item,
                () => _deleteOrder([item.orderId ?? '']),
              ),
          onSelectionChanged:
              (selectedItem) => context
                  .read<DerivativesConditionOrderCubit>()
                  .toggleSelectItem(selectedItem),
        );
      },
    );
  }

  void _deleteOrder(List<String> orderIds) {
    _deleteUpdateOrderCubit.deleteFUConditionOrder(
      DeleteFuConditionOrderRequest(
        accountId:
            GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
        requestId: "app_${AppHelper().genXRequestID()}",
        via: "V",
        fuConfitionOrderIds: orderIds,
      ),
    );
  }
}
