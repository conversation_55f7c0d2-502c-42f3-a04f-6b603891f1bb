import 'package:flutter/material.dart';
import 'package:vp_common/generated/assets.gen.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/router/trading_router.dart';
import 'package:vp_trading/screen/derivative_home_cubit.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/base_scafold_many_titles.dart';
import 'package:vp_trading/screen/place_order/derivative/widget/button_widget.dart';

class SignUpSuccessPage extends StatelessWidget {
  const SignUpSuccessPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BaseScaffoldManyTitles(
      leading: const SizedBox.shrink(),
      body: Column(
        children: [
          Assets.icons.icSuccess.svg(width: 120),
          const SizedBox(height: 24),
          Text(
            VPTradingLocalize.current.derivative_sign_up_success,
            style: vpTextStyle.headineBold6.copyColor(themeData.primary),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              VPTradingLocalize.current.derivative_we_will_notify_you_account,
              style: vpTextStyle.subtitle16.copyColor(themeData.gray700),
              textAlign: TextAlign.center,
            ),
          ),
          const Spacer(),
          const DividerWidget(),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ButtonWidget(
              action:
                  VPTradingLocalize.current.derivative_back_to_market_screen,
              onPressed: () {
                context.popUntilRoute(
                  TradingRouter.derivativeHomePage.routeName,
                );
                context.read<DerivativeHomeCubit>().onIndexChanged(0);
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
