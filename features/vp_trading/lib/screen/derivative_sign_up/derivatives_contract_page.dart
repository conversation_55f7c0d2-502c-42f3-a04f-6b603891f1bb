import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/derivative_contract/derivatives_contract_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/router/trading_router.dart';
import 'package:vp_trading/screen/derivative_home_cubit.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/base_scafold_many_titles.dart';
import 'package:vp_trading/screen/derivative_sign_up/widgets/bottom_navigation/bottom_navigation_widget.dart';

class DerivativesContractPage extends StatefulWidget {
  const DerivativesContractPage({super.key});

  @override
  State<DerivativesContractPage> createState() =>
      _DerivativesContractPageState();
}

class _DerivativesContractPageState extends State<DerivativesContractPage> {
  late DerivativesContractCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = DerivativesContractCubit();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffoldManyTitles(
      title: VPTradingLocalize.current.derivative_future,
      subTitle: VPTradingLocalize.current.derivative_contract,
      resizeToAvoidBottomInset: true,
      leading: Container(),
      leadingWidth: 16,
      rightAction: InkWell(
        onTap: () {
          context.pop();
        },
        child: Assets.icons.icClose.svg(),
      ),
      isIgnoreRightAction: false,
      body: BlocSelector<
        DerivativesContractCubit,
        DerivativesContractState,
        bool
      >(
        bloc: _cubit,
        selector: (state) => state.isLoading,
        builder: (context, state) {
          return Stack(
            children: [
              Column(
                children: [
                  Expanded(
                    child: LayoutBuilder(
                      builder: (
                        BuildContext context,
                        BoxConstraints constraints,
                      ) {
                        double height = constraints.maxHeight;
                        return ExpansionTile(
                          initiallyExpanded: true,
                          title: Row(
                            children: [
                              Text(
                                VPTradingLocalize
                                    .current
                                    .derivative_terms_and_conditions,
                                style: vpTextStyle.subtitle16.copyColor(
                                  vpColor.textPrimary,
                                ),
                              ),
                              const Spacer(),
                              InkWell(
                                onTap: () {
                                  _cubit.downloadContract(
                                    context,
                                    'derivative_sign_up.pdf',
                                    AppConstants.derivativeContract,
                                  );
                                },
                                child: VpTradingAssets.icons.icDownload.svg(
                                  color:
                                      context.isDarkMode
                                          ? null
                                          : themeData.gray900,
                                ),
                              ),
                            ],
                          ),
                          shape: const Border(),
                          tilePadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                          ),
                          iconColor: themeData.primary,
                          textColor: themeData.gray900,
                          collapsedIconColor: themeData.primary,
                          children: [
                            SizedBox(
                              height: height - 56,
                              child: FutureBuilder<Uint8List?>(
                                future: _cubit.getContractData(),
                                builder: (cxt, snapshot) {
                                  if (snapshot.hasData &&
                                      snapshot.data != null) {
                                    return SfPdfViewer.memory(
                                      snapshot.data!,
                                      onDocumentLoaded: (value) {},
                                      onDocumentLoadFailed: (value) {},
                                      canShowScrollHead: false,
                                    );
                                  } else if (snapshot.hasError) {
                                    final message = snapshot.error.toString();
                                    return Center(
                                      child: Text(
                                        message.isEmpty
                                            ? VPCommonLocalize
                                                .current
                                                .err_unknown
                                            : message,
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  const DividerWidget(),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: RichText(
                      text: TextSpan(
                        text:
                            VPTradingLocalize
                                .current
                                .derivative_by_pressing_the_button,
                        style: vpTextStyle.subtitle14?.copyWith(
                          color: themeData.gray700,
                          height: 1.5,
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text:
                                ' "${VPTradingLocalize.current.derivative_complete}" ',
                            style: vpTextStyle.subtitle14.copyColor(
                              themeData.primary,
                            ),
                          ),
                          TextSpan(
                            text:
                                VPTradingLocalize
                                    .current
                                    .derivative_confirm_and_agree_terms,
                            style: vpTextStyle.subtitle14.copyColor(
                              themeData.gray700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const DividerWidget(),
                  BottomNavigationWidget(
                    confirm: () async {
                      _cubit.openOrReactiveAccountDerivatives(
                        () {
                          final cubit = context.read<DerivativeHomeCubit>();
                          context.push(
                            TradingRouter.derivativeSignUpSuccess.routeName,
                            extra: cubit,
                          );
                        },
                        () {
                          context.pop();
                        },
                      );
                    },
                    enableBtnNext: !state,
                  ),
                ],
              ),
              if (state) const Center(child: VPBankLoading()),
            ],
          );
        },
      ),
    );
  }
}
