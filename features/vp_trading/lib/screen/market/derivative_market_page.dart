import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/derivative/derivative_market/derivatives_market_cubit.dart';
import 'package:vp_trading/cubit/derivative/mixin/derivative_account_mixin.dart';
import 'package:vp_trading/router/trading_router.dart';
import 'package:vp_trading/screen/derivative_home_cubit.dart';
import 'package:vp_trading/screen/derivative_sign_up/sign_up_await_approve_widget.dart';
import 'package:vp_trading/screen/derivative_sign_up/signed_up_widget.dart';
import 'package:vp_trading/screen/market/derivative_impact_chart/impact_stock_chart.dart';
import 'package:vp_trading/screen/market/market_index/derivative_market_index_item_view.dart';
import 'package:vp_trading/screen/market/widgets/derivative_info_view.dart';
import 'package:vp_trading/widgets/derivative_sign_up_now_view.dart';

class DerivativeMarketPage extends StatefulWidget {
  const DerivativeMarketPage({this.onItemTap, super.key});

  final ValueChanged<StockInfoModel>? onItemTap;

  @override
  State<DerivativeMarketPage> createState() => _DerivativeMarketPageState();
}

class _DerivativeMarketPageState extends State<DerivativeMarketPage>
    with AutomaticKeepAliveClientMixin, DerivativeAccountMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider<DerivativesMarketCubit>(
      create: (context) => DerivativesMarketCubit()..loadData(),
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevationMinus1,
        appBar: VPAppBar.layer(
          title: 'Thị trường',
          subTitle: 'Phái sinh',
          revertSubTitle: true,
        ),
        body: SingleChildScrollView(
          child: Column(
            spacing: 12,
            children: [
              if (isShowDerivativeSignUpAccount)
                DerivativeSignUpNowView(
                  onTap: () {
                    final cubit = context.read<DerivativeHomeCubit>();
                    context
                        .push(
                          TradingRouter.derivativeContract.routeName,
                          extra: cubit,
                        )
                        .then((value) => setState(() {}));
                  },
                ),
              if (isShowAwaitApprove) const SignUpAwaitApproveWidget(),
              if (isShowDerivativeAssets)
                BlocBuilder<DerivativesMarketCubit, DerivativesMarketState>(
                  builder: (context, state) {
                    return SignedUpWidget(model: state.fuAssetSummary);
                  },
                ),

              MarketIndexList(
                indexCodes: const [
                  IndexCode.VN30,
                  IndexCode.VNINDEX,
                  IndexCode.HNXINDEX,
                  IndexCode.UPCOMINDEX,
                ],
                primaryBuilder:
                    (marketInfo) =>
                        DerivativeMarketIndexItemView(marketInfo: marketInfo),
              ),

              DerivativeInfoView(
                stockType: StockType.FUVN30,
                onTap: widget.onItemTap,
              ),

              const ImpactStockChart(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
