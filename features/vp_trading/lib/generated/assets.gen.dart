// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/arrow_drop.svg
  SvgGenImage get arrowDrop => const SvgGenImage('assets/icons/arrow_drop.svg');

  /// Directory path: assets/icons/bottom_bar
  $AssetsIconsBottomBarGen get bottomBar => const $AssetsIconsBottomBarGen();

  /// File path: assets/icons/derivatives_position_close.svg
  SvgGenImage get derivativesPositionClose =>
      const SvgGenImage('assets/icons/derivatives_position_close.svg');

  /// File path: assets/icons/derivatives_position_shuffle.svg
  SvgGenImage get derivativesPositionShuffle =>
      const SvgGenImage('assets/icons/derivatives_position_shuffle.svg');

  /// File path: assets/icons/handle.svg
  SvgGenImage get handle => const SvgGenImage('assets/icons/handle.svg');

  /// File path: assets/icons/ic_add.svg
  SvgGenImage get icAdd => const SvgGenImage('assets/icons/ic_add.svg');

  /// File path: assets/icons/ic_arrow_left.svg
  SvgGenImage get icArrowLeft =>
      const SvgGenImage('assets/icons/ic_arrow_left.svg');

  /// File path: assets/icons/ic_arrow_right.svg
  SvgGenImage get icArrowRight =>
      const SvgGenImage('assets/icons/ic_arrow_right.svg');

  /// File path: assets/icons/ic_asking_circle.svg
  SvgGenImage get icAskingCircle =>
      const SvgGenImage('assets/icons/ic_asking_circle.svg');

  /// File path: assets/icons/ic_asset.svg
  SvgGenImage get icAsset => const SvgGenImage('assets/icons/ic_asset.svg');

  /// File path: assets/icons/ic_cancel_multi_order.svg
  SvgGenImage get icCancelMultiOrder =>
      const SvgGenImage('assets/icons/ic_cancel_multi_order.svg');

  /// File path: assets/icons/ic_cancle_dialog.svg
  SvgGenImage get icCancleDialog =>
      const SvgGenImage('assets/icons/ic_cancle_dialog.svg');

  /// File path: assets/icons/ic_chart.png
  AssetGenImage get icChart => const AssetGenImage('assets/icons/ic_chart.png');

  /// File path: assets/icons/ic_close.svg
  SvgGenImage get icClose => const SvgGenImage('assets/icons/ic_close.svg');

  /// File path: assets/icons/ic_command_history.svg
  SvgGenImage get icCommandHistory =>
      const SvgGenImage('assets/icons/ic_command_history.svg');

  /// File path: assets/icons/ic_command_history2.svg
  SvgGenImage get icCommandHistory2 =>
      const SvgGenImage('assets/icons/ic_command_history2.svg');

  /// File path: assets/icons/ic_condition_buy.svg
  SvgGenImage get icConditionBuy =>
      const SvgGenImage('assets/icons/ic_condition_buy.svg');

  /// File path: assets/icons/ic_condition_sell.svg
  SvgGenImage get icConditionSell =>
      const SvgGenImage('assets/icons/ic_condition_sell.svg');

  /// File path: assets/icons/ic_derivative_account_pending_approve.svg
  SvgGenImage get icDerivativeAccountPendingApprove => const SvgGenImage(
    'assets/icons/ic_derivative_account_pending_approve.svg',
  );

  /// File path: assets/icons/ic_derivative_invalid_account.svg
  SvgGenImage get icDerivativeInvalidAccount =>
      const SvgGenImage('assets/icons/ic_derivative_invalid_account.svg');

  /// File path: assets/icons/ic_down.svg
  SvgGenImage get icDown => const SvgGenImage('assets/icons/ic_down.svg');

  /// File path: assets/icons/ic_download.svg
  SvgGenImage get icDownload =>
      const SvgGenImage('assets/icons/ic_download.svg');

  /// File path: assets/icons/ic_edit.svg
  SvgGenImage get icEdit => const SvgGenImage('assets/icons/ic_edit.svg');

  /// File path: assets/icons/ic_filter.svg
  SvgGenImage get icFilter => const SvgGenImage('assets/icons/ic_filter.svg');

  /// File path: assets/icons/ic_greater_than_equal.svg
  SvgGenImage get icGreaterThanEqual =>
      const SvgGenImage('assets/icons/ic_greater_than_equal.svg');

  /// File path: assets/icons/ic_green_chart.svg
  SvgGenImage get icGreenChart =>
      const SvgGenImage('assets/icons/ic_green_chart.svg');

  /// File path: assets/icons/ic_growth.svg
  SvgGenImage get icGrowth => const SvgGenImage('assets/icons/ic_growth.svg');

  /// File path: assets/icons/ic_have_filter.svg
  SvgGenImage get icHaveFilter =>
      const SvgGenImage('assets/icons/ic_have_filter.svg');

  /// File path: assets/icons/ic_income_statement.svg
  SvgGenImage get icIncomeStatement =>
      const SvgGenImage('assets/icons/ic_income_statement.svg');

  /// File path: assets/icons/ic_less_than_equal.svg
  SvgGenImage get icLessThanEqual =>
      const SvgGenImage('assets/icons/ic_less_than_equal.svg');

  /// File path: assets/icons/ic_list.svg
  SvgGenImage get icList => const SvgGenImage('assets/icons/ic_list.svg');

  /// File path: assets/icons/ic_list2.svg
  SvgGenImage get icList2 => const SvgGenImage('assets/icons/ic_list2.svg');

  /// File path: assets/icons/ic_none_data.svg
  SvgGenImage get icNoneData =>
      const SvgGenImage('assets/icons/ic_none_data.svg');

  /// File path: assets/icons/ic_order.svg
  SvgGenImage get icOrder => const SvgGenImage('assets/icons/ic_order.svg');

  /// File path: assets/icons/ic_order_buy.svg
  SvgGenImage get icOrderBuy =>
      const SvgGenImage('assets/icons/ic_order_buy.svg');

  /// File path: assets/icons/ic_order_gtc.svg
  SvgGenImage get icOrderGtc =>
      const SvgGenImage('assets/icons/ic_order_gtc.svg');

  /// File path: assets/icons/ic_order_lo.svg
  SvgGenImage get icOrderLo =>
      const SvgGenImage('assets/icons/ic_order_lo.svg');

  /// File path: assets/icons/ic_order_sell.svg
  SvgGenImage get icOrderSell =>
      const SvgGenImage('assets/icons/ic_order_sell.svg');

  /// File path: assets/icons/ic_order_sl.svg
  SvgGenImage get icOrderSl =>
      const SvgGenImage('assets/icons/ic_order_sl.svg');

  /// File path: assets/icons/ic_order_tp.svg
  SvgGenImage get icOrderTp =>
      const SvgGenImage('assets/icons/ic_order_tp.svg');

  /// File path: assets/icons/ic_order_waiting.svg
  SvgGenImage get icOrderWaiting =>
      const SvgGenImage('assets/icons/ic_order_waiting.svg');

  /// File path: assets/icons/ic_price_list.svg
  SvgGenImage get icPriceList =>
      const SvgGenImage('assets/icons/ic_price_list.svg');

  /// File path: assets/icons/ic_price_list2.svg
  SvgGenImage get icPriceList2 =>
      const SvgGenImage('assets/icons/ic_price_list2.svg');

  /// File path: assets/icons/ic_remove_2.svg
  SvgGenImage get icRemove2 =>
      const SvgGenImage('assets/icons/ic_remove_2.svg');

  /// File path: assets/icons/ic_same.svg
  SvgGenImage get icSame => const SvgGenImage('assets/icons/ic_same.svg');

  /// File path: assets/icons/ic_signup.svg
  SvgGenImage get icSignup => const SvgGenImage('assets/icons/ic_signup.svg');

  /// File path: assets/icons/ic_signup_approve.svg
  SvgGenImage get icSignupApprove =>
      const SvgGenImage('assets/icons/ic_signup_approve.svg');

  /// File path: assets/icons/ic_signup_approve_light.svg
  SvgGenImage get icSignupApproveLight =>
      const SvgGenImage('assets/icons/ic_signup_approve_light.svg');

  /// File path: assets/icons/ic_signup_light.svg
  SvgGenImage get icSignupLight =>
      const SvgGenImage('assets/icons/ic_signup_light.svg');

  /// File path: assets/icons/ic_subtract.svg
  SvgGenImage get icSubtract =>
      const SvgGenImage('assets/icons/ic_subtract.svg');

  /// File path: assets/icons/ic_ticked.svg
  SvgGenImage get icTicked => const SvgGenImage('assets/icons/ic_ticked.svg');

  /// File path: assets/icons/ic_tool_tip.svg
  SvgGenImage get icToolTip =>
      const SvgGenImage('assets/icons/ic_tool_tip.svg');

  /// File path: assets/icons/ic_transaction_history.svg
  SvgGenImage get icTransactionHistory =>
      const SvgGenImage('assets/icons/ic_transaction_history.svg');

  /// File path: assets/icons/ic_transaction_money.svg
  SvgGenImage get icTransactionMoney =>
      const SvgGenImage('assets/icons/ic_transaction_money.svg');

  /// File path: assets/icons/ic_up.svg
  SvgGenImage get icUp => const SvgGenImage('assets/icons/ic_up.svg');

  /// File path: assets/icons/ic_utils.svg
  SvgGenImage get icUtils => const SvgGenImage('assets/icons/ic_utils.svg');

  /// File path: assets/icons/ic_utils2.svg
  SvgGenImage get icUtils2 => const SvgGenImage('assets/icons/ic_utils2.svg');

  /// File path: assets/icons/ic_warning.svg
  SvgGenImage get icWarning => const SvgGenImage('assets/icons/ic_warning.svg');

  /// File path: assets/icons/ic_white_chart.svg
  SvgGenImage get icWhiteChart =>
      const SvgGenImage('assets/icons/ic_white_chart.svg');

  /// File path: assets/icons/order_cancel_icon.svg
  SvgGenImage get orderCancelIcon =>
      const SvgGenImage('assets/icons/order_cancel_icon.svg');

  /// List of all assets
  List<dynamic> get values => [
    arrowDrop,
    derivativesPositionClose,
    derivativesPositionShuffle,
    handle,
    icAdd,
    icArrowLeft,
    icArrowRight,
    icAskingCircle,
    icAsset,
    icCancelMultiOrder,
    icCancleDialog,
    icChart,
    icClose,
    icCommandHistory,
    icCommandHistory2,
    icConditionBuy,
    icConditionSell,
    icDerivativeAccountPendingApprove,
    icDerivativeInvalidAccount,
    icDown,
    icDownload,
    icEdit,
    icFilter,
    icGreaterThanEqual,
    icGreenChart,
    icGrowth,
    icHaveFilter,
    icIncomeStatement,
    icLessThanEqual,
    icList,
    icList2,
    icNoneData,
    icOrder,
    icOrderBuy,
    icOrderGtc,
    icOrderLo,
    icOrderSell,
    icOrderSl,
    icOrderTp,
    icOrderWaiting,
    icPriceList,
    icPriceList2,
    icRemove2,
    icSame,
    icSignup,
    icSignupApprove,
    icSignupApproveLight,
    icSignupLight,
    icSubtract,
    icTicked,
    icToolTip,
    icTransactionHistory,
    icTransactionMoney,
    icUp,
    icUtils,
    icUtils2,
    icWarning,
    icWhiteChart,
    orderCancelIcon,
  ];
}

class $AssetsIconsBottomBarGen {
  const $AssetsIconsBottomBarGen();

  /// File path: assets/icons/bottom_bar/ic_category.svg
  SvgGenImage get icCategory =>
      const SvgGenImage('assets/icons/bottom_bar/ic_category.svg');

  /// File path: assets/icons/bottom_bar/ic_category_selected.svg
  SvgGenImage get icCategorySelected =>
      const SvgGenImage('assets/icons/bottom_bar/ic_category_selected.svg');

  /// File path: assets/icons/bottom_bar/ic_market.svg
  SvgGenImage get icMarket =>
      const SvgGenImage('assets/icons/bottom_bar/ic_market.svg');

  /// File path: assets/icons/bottom_bar/ic_market_selected.svg
  SvgGenImage get icMarketSelected =>
      const SvgGenImage('assets/icons/bottom_bar/ic_market_selected.svg');

  /// File path: assets/icons/bottom_bar/ic_order.svg
  SvgGenImage get icOrder =>
      const SvgGenImage('assets/icons/bottom_bar/ic_order.svg');

  /// File path: assets/icons/bottom_bar/ic_order_book.svg
  SvgGenImage get icOrderBook =>
      const SvgGenImage('assets/icons/bottom_bar/ic_order_book.svg');

  /// File path: assets/icons/bottom_bar/ic_order_book_selected.svg
  SvgGenImage get icOrderBookSelected =>
      const SvgGenImage('assets/icons/bottom_bar/ic_order_book_selected.svg');

  /// File path: assets/icons/bottom_bar/ic_order_selected.svg
  SvgGenImage get icOrderSelected =>
      const SvgGenImage('assets/icons/bottom_bar/ic_order_selected.svg');

  /// File path: assets/icons/bottom_bar/ic_service.svg
  SvgGenImage get icService =>
      const SvgGenImage('assets/icons/bottom_bar/ic_service.svg');

  /// File path: assets/icons/bottom_bar/ic_service_selected.svg
  SvgGenImage get icServiceSelected =>
      const SvgGenImage('assets/icons/bottom_bar/ic_service_selected.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    icCategory,
    icCategorySelected,
    icMarket,
    icMarketSelected,
    icOrder,
    icOrderBook,
    icOrderBookSelected,
    icOrderSelected,
    icService,
    icServiceSelected,
  ];
}

class VpTradingAssets {
  const VpTradingAssets._();

  static const String package = 'vp_trading';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  static const String package = 'vp_trading';

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_trading/$_assetName';
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_trading';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_trading/$_assetName';
}
