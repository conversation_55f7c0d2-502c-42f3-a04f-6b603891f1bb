class CancelConditionOrderRequestModel {
  String? accountId;
  String? authtype;
  EngineInput? engineInput;
  String? classcd;
  String? orderid;
  bool? isSaveVerify;
  String? requestId;
  String? tokenid;
  String? transactionId;

  CancelConditionOrderRequestModel({
    this.accountId,
    this.authtype,
    this.engineInput,
    this.classcd,
    this.orderid,
    this.isSaveVerify,
    this.requestId,
    this.tokenid,
    this.transactionId,
  });

  CancelConditionOrderRequestModel.fromJson(Map<String, dynamic> json) {
    accountId = json['accountId'];
    authtype = json['authtype'];
    engineInput =
        json['engineInput'] != null
            ? EngineInput.fromJson(json['engineInput'])
            : null;
    classcd = json['classcd'];
    orderid = json['orderid'];
    isSaveVerify = json['isSaveVerify'];
    requestId = json['requestId'];
    tokenid = json['tokenid'];
    transactionId = json['transactionId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['accountId'] = accountId;
    data['authtype'] = authtype;
    if (engineInput != null) {
      data['engineInput'] = engineInput!.toJson();
    }
    data['classcd'] = classcd;
    data['orderid'] = orderid;
    data['isSaveVerify'] = isSaveVerify;
    data['requestId'] = requestId;
    data['tokenid'] = tokenid;
    data['transactionId'] = transactionId;
    return data;
  }
}

class EngineInput {
  String? orderid;
  String? classcd;

  EngineInput({this.orderid, this.classcd});

  EngineInput.fromJson(Map<String, dynamic> json) {
    orderid = json['orderid'];
    classcd = json['classcd'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orderid'] = orderid;
    data['classcd'] = classcd;
    return data;
  }
}
