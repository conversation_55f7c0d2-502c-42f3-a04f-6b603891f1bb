enum ActivationConditionsType { greaterThan, lessThan }

extension ActivationConditionsTypeExt on ActivationConditionsType {
  String toParamRequest() {
    switch (this) {
      case ActivationConditionsType.greaterThan:
        return 'UP';
      case ActivationConditionsType.lessThan:
        return 'DOWN';
    }
  }

  String getSymbol() {
    switch (this) {
      case ActivationConditionsType.greaterThan:
        return '≥';
      case ActivationConditionsType.lessThan:
        return '≤';
    }
  }

  bool get isGreaterThan => this == ActivationConditionsType.greaterThan;

  bool get isLessThan => this == ActivationConditionsType.lessThan;

  /// Supports: UP, DOWN, GE, LE, GREATER_THAN, LESS_THAN
  static ActivationConditionsType fromString(String? activeType) {
    switch (activeType?.toUpperCase()) {
      case 'UP':
        return ActivationConditionsType.greaterThan;
      case 'DOWN':
        return ActivationConditionsType.lessThan;
      default:
        return ActivationConditionsType.lessThan;
    }
  }
}
