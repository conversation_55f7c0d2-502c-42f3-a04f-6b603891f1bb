import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_trading/model/enum/order_lot_type.dart';

part 'stock_info_state.dart';

class StockInfoCubit extends Cubit<StockInfoState>
    with StockInfoSocketMixin, MarketSessionSocketMixin {
  StockInfoCubit({OrderLotType orderLotType = OrderLotType.roundLot})
    : super(
        StockInfoState(status: ApiStatus.initial(), orderLotType: orderLotType),
      );

  final StockCommonRepository _stockCommonRepository = GetIt.instance.get();

  final AssetRepository _assetRepository = GetIt.instance.get();

  SubAccountModel? get derivativeAccount =>
      GetIt.instance.get<SubAccountCubit>().derivativeActiveAccount;

  final StreamController<String> _priceStreamController =
      StreamController<String>.broadcast();

  Stream<String> get priceStream => _priceStreamController.stream;

  num? currentPrice;

  void updatePrice(String price) {
    _priceStreamController.add(price);
  }

  List<dynamic> bidPrices = [];

  @override
  void onSocketMarketSessionListener(VPMarketSessionData data) {
    if (state.stockInfo?.symbol == data.symbol) {
      emit(
        state.copyWith(
          marketStatus: MarketStatusModel(
            marketStatus: data.marketStatus!,
            marketCode: data.marketCode,
          ),
        ),
      );
    }
  }

  @override
  void onSocketStockInfoListener(VPStockInfoData data) {
    final stockInfo = state.stockInfo;

    if (stockInfo?.symbol == data.symbol) {
      if (state.isRealtime &&
          (data.closePrice != null || data.reference != null)) {
        bool isDerivative = (data.symbol?.length ?? 0) > 6;
        var price = data.closePrice ?? data.reference!;
        updatePrice(
          isDerivative
              ? price.toFormatThousandSeparator()
              : price.getPriceFormatted(convertToThousand: true),
        );
      }
      final bidPrice1 = data.bidPrice1 ?? stockInfo?.bidPrice1;
      final bidPrice2 = data.bidPrice2 ?? stockInfo?.bidPrice2;
      final bidPrice3 = data.bidPrice3 ?? stockInfo?.bidPrice3;
      currentPrice = data.closePrice ?? data.reference;
      final priceNum =
          bidPrice1 is num
              ? bidPrice1
              : num.tryParse(bidPrice1?.toString() ?? '');

      bidPrices = [
        FormatUtils.formatClosePrice(priceNum, trimZero: false),
        FormatUtils.formatClosePrice(bidPrice2, trimZero: false),
        FormatUtils.formatClosePrice(bidPrice3, trimZero: false),
      ];

      final offerPrice1 = data.offerPrice1 ?? stockInfo?.offerPrice1;
      final offerPrice2 = data.offerPrice2 ?? stockInfo?.offerPrice2;
      final offerPrice3 = data.offerPrice3 ?? stockInfo?.offerPrice3;
    }
  }

  void getBidPrice(StockInfoModel? stockInfo) {
    try {
      final closePrice = stockInfo?.closePrice ?? 0;

       currentPrice = closePrice > 0 ? closePrice : stockInfo?.refPrice;

      final bidPrice1 = stockInfo?.bidPrice1;
      final bidPrice2 = stockInfo?.bidPrice2;
      final bidPrice3 = stockInfo?.bidPrice3;
      final priceNum =
          bidPrice1 is num
              ? bidPrice1
              : num.tryParse(bidPrice1?.toString() ?? '');

      bidPrices = [
        FormatUtils.formatClosePrice(priceNum as num?, trimZero: false),
        FormatUtils.formatClosePrice(bidPrice2, trimZero: false),
        FormatUtils.formatClosePrice(bidPrice3, trimZero: false),
      ];
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      bidPrices = [];
    }
  }

  void loadFuQuotes() async {
    if (state.fuQuotes != null) return;

    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final data = await _stockCommonRepository.fuQuote();

      final quotes =
          data
              ?.where(
                (e) =>
                    !e.isCS &&
                    (derivativeAccount?.isProInvestor == true
                        ? e.isTpcpOrHDTL
                        : e.isHDTL),
              )
              .toList();

      emit(state.copyWith(status: ApiStatus.done(), fuQuotes: quotes));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  Future<List<MarketStatusModel>?> _getMarketStatus() async {
    try {
      return _stockCommonRepository.getMarketStatus();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  void loadData(String symbol, {bool isDerivative = false}) async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      if (isDerivative) {
        final data = await Future.wait([
          _stockCommonRepository.getFuStockDetailFor(
            requiredMarketInfo: true,
            symbol: symbol,
          ),

          state.fuQuotes != null
              ? Future.value(state.fuQuotes)
              : _stockCommonRepository.fuQuote(),

          if (derivativeAccount != null)
            state.fuAssetSummary != null
                ? Future.value(state.fuAssetSummary)
                : _assetRepository.getSummaryAccounts(derivativeAccount!.id),

          _getMarketStatus(),
        ]);

        final stockInfoData =
            data.getElementAt(0) as (StockInfoModel?, MarketInfoModel?)?;

        final quotes = data.getElementAt(1) as List<FuQuoteModel>?;

        final fuAssetSummary = data.getElementAt(2) as AsssetSummaryModel?;

        final marketStatus = data.getElementAt(3) as List<MarketStatusModel>?;

        final stockInfo = stockInfoData?.$1?.updateByFuQuote(
          quotes?.findFirstOrNull((e) => e.symbol == symbol),
        );

        getBidPrice(stockInfoData?.$1);

        updatePrice(
          (stockInfo?.closePrice ?? stockInfo?.refPrice ?? 0)
              .toFormatThousandSeparator(),
        );

        emit(
          state.copyWith(
            status: ApiStatus.done(),
            stockInfo: stockInfo,
            allowStockInfoNullable: true,
            fuMarketInfo: stockInfoData?.$2,
            fuAssetSummary: fuAssetSummary,
            marketStatus: marketStatus?.findFirstOrNull(
              (e) => e.marketCode == stockInfo?.marketCode?.value,
            ),
            fuQuotes:
                quotes
                    ?.where(
                      (e) =>
                          !e.isCS &&
                          (derivativeAccount?.isProInvestor == true
                              ? e.isTpcpOrHDTL
                              : e.isHDTL),
                    )
                    .toList(),
          ),
        );
      } else {
        final data = await Future.wait([
          _stockCommonRepository.getStockDetailFor(
            isOddLot: state.orderLotType.isOddLot,
            requiredQuoteData: true,
            symbol: symbol,
          ),

          _getMarketStatus(),
        ]);

        final stockInfo = data.getElementAt(0) as StockInfoModel?;

        final marketStatus = data.getElementAt(1) as List<MarketStatusModel>?;

        getBidPrice(stockInfo);

        emit(
          state.copyWith(
            status: ApiStatus.done(),
            allowStockInfoNullable: true,
            stockInfo: stockInfo,
            marketStatus: marketStatus?.findFirstOrNull(
              (e) => e.marketCode == stockInfo?.marketCode?.value,
            ),
          ),
        );
      }

      subscribeSocket(symbol);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(
        state.copyWith(
          stockInfo: null,
          allowStockInfoNullable: true,
          status: ApiStatus.error(e),
        ),
      );
    }
  }

  void onOrderLotTypeChanged(OrderLotType orderLotType) {
    if (state.orderLotType == orderLotType) return;

    emit(state.copyWith(orderLotType: orderLotType));
  }

  void subscribeSocket(String symbol) {
    unsubscribeStockInfo();
    subscribeStockInfo({symbol});

    unsubscribeMarketSession();
    subscribeMarketSession({symbol});
  }

  void onRealtimeChanged(bool isRealtime) {
    if (isRealtime) {
      bool isDerivative = state.stockInfo?.stockType?.isFU ?? false;
      updatePrice(
        isDerivative
            ? currentPrice?.toFormatThousandSeparator() ?? ""
            : currentPrice?.getPriceFormatted(convertToThousand: true) ?? "",
      );
    }
    emit(state.copyWith(isRealtime: isRealtime));
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    unsubscribeMarketSession();
    _priceStreamController.close();
    return super.close();
  }
}
