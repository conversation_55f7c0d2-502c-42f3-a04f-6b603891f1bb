# Bug Fix: Date Handling in Edit Take Profit/Stop Loss Order

## 🐛 Problem Description

**Issue**: When editing a Take Profit/Stop Loss order, the app was sending default dates (current date) to the backend API instead of preserving the original dates from the API, even when the user only changed other fields (trigger value, slippage, volume).

**Expected Behavior**: 
- A<PERSON> should preserve original `fromDate` and `toDate` from API when user doesn't change date fields
- A<PERSON> should only send new dates when user explicitly modifies the effective time

**Actual Behavior**:
- <PERSON><PERSON> was always sending current date (`30/09/2025`, `03/10/2025`) regardless of original API dates
- This happened even when user only changed non-date fields

## 🔍 Root Cause Analysis

The bug was caused by multiple issues in the date handling logic:

### 1. EditEffectiveTimeWidget Initialization
```dart
// BEFORE (Problematic)
_startDate = widget.initialFromDate ?? DateTime.now();
_endDate = widget.initialToDate ?? DateTime.now();

WidgetsBinding.instance.addPostFrameCallback((_) {
  context.read<ValidateConditionOrderCubit>().setEffectiveTime(
    _startDate,
    _endDate,
  );
});
```

**Problem**: When `initialFromDate`/`initialToDate` were `null` (due to parsing issues), the widget would use `DateTime.now()` and immediately set it in the cubit, overriding any original dates.

### 2. Date Comparison Logic
The `_hasFieldsChanged()` method had complex logic that didn't properly detect when dates were unchanged.

### 3. Final Date Selection Logic
The `_handleSave()` method's logic for choosing between original and current dates was not robust enough.

## ✅ Solution Implementation

### 1. Fixed EditEffectiveTimeWidget Initialization
```dart
// AFTER (Fixed)
WidgetsBinding.instance.addPostFrameCallback((_) {
  // Only set effective time if we have valid initial dates from API
  // This prevents overriding original dates with current date
  if (widget.initialFromDate != null && widget.initialToDate != null) {
    context.read<ValidateConditionOrderCubit>().setEffectiveTime(
      _startDate,
      _endDate,
    );
  }
});
```

**Fix**: Only call `setEffectiveTime()` when we have valid dates from API, preventing automatic override with current date.

### 2. Improved Date Change Detection
```dart
// Simplified and more reliable logic
if (originalFromDate.isNotEmpty && currentFromDate.isNotEmpty) {
  datesChanged = originalFromDate != currentFromDate;
} else if (originalToDate.isNotEmpty && currentToDate.isNotEmpty) {
  datesChanged = datesChanged || (originalToDate != currentToDate);
}

// If validateConditionState has no dates but original has dates,
// this means user didn't change dates, so no change detected
if (currentFromDate.isEmpty && currentToDate.isEmpty && 
    (originalFromDate.isNotEmpty || originalToDate.isNotEmpty)) {
  datesChanged = false;
}
```

### 3. Enhanced Final Date Selection Logic
```dart
// Always prioritize original dates from API unless user explicitly changed them
final finalFromDate = currentFromDate.isNotEmpty && 
                     currentFromDate != originalFromDate &&
                     originalFromDate.isNotEmpty
    ? currentFromDate
    : originalFromDate;
    
final finalToDate = currentToDate.isNotEmpty && 
                   currentToDate != originalToDate &&
                   originalToDate.isNotEmpty
    ? currentToDate
    : originalToDate;
```

**Fix**: More explicit logic that prioritizes original dates unless user made explicit changes.

## 🧪 Testing

Created comprehensive tests in `edit_take_profit_stop_loss_order_widget_test.dart` to verify:

1. **Preserve Original Dates**: When user changes other fields but not dates, original dates are preserved
2. **Use New Dates**: When user explicitly changes dates, new dates are used
3. **Edge Cases**: Handle null/empty date scenarios properly

## 📋 Files Modified

1. `features/vp_trading/lib/screen/order_container/conditional_order/edit_condition_order/edit_effective_time_widget.dart`
   - Fixed initialization logic to prevent automatic date override

2. `features/vp_trading/lib/screen/order_container/conditional_order/condition_profit_stop_loss_order/edit_take_profit_stop_loss_order_widget.dart`
   - Improved `_hasFieldsChanged()` date comparison logic
   - Enhanced `_handleSave()` final date selection logic
   - Added better comments and error handling

3. `features/vp_trading/test/screen/order_container/conditional_order/edit_take_profit_stop_loss_order_widget_test.dart`
   - Added comprehensive test coverage for date handling scenarios

## 🎯 Impact

- ✅ Fixed: App now preserves original API dates when user doesn't change them
- ✅ Fixed: App only sends new dates when user explicitly modifies effective time
- ✅ Improved: More reliable date change detection
- ✅ Enhanced: Better error handling and code documentation

## 🔄 Verification Steps

1. Load an existing Take Profit/Stop Loss order with specific `fromDate`/`toDate`
2. Modify only trigger value, slippage, or volume (not dates)
3. Save the order
4. Verify that API request contains original `fromDate`/`toDate`, not current date
5. Modify the effective time dates
6. Save the order
7. Verify that API request contains the new dates
