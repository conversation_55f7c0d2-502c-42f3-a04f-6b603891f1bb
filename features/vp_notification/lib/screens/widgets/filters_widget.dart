import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_notification/data/model/category_notify_model.dart';

class FiltersWidget extends StatelessWidget {
  final CategoryNotifyModel? selected;
  final ValueChanged<CategoryNotifyModel> onTap;
  final List<CategoryNotifyModel> categories;

  const FiltersWidget({
    Key? key,
    required this.selected,
    required this.onTap,
    required this.categories,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: themeData.bgMain,
      height: 62,
      child: ListView.separated(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
        scrollDirection: Axis.horizontal,
        itemBuilder: (_, index) {
          final category = categories[index];
          final bool unRead = (category.countUnread ?? 0) > 0;
          return ChoiceChip(
            showCheckmark: false,
            onSelected: (value) {
              onTap(category);
            },
            selectedColor: themeData.primary,
            backgroundColor: themeData.highlightBg,
            shape: StadiumBorder(),
            label: Stack(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: unRead ? 3 : 0),
                  child: Text(
                    categories[index].nameVi ?? '',
                    style: vpTextStyle.subtitle14?.copyWith(
                      color:
                          category.id == selected?.id
                              ? themeData.textEnable
                              : themeData.black,
                    ),
                  ),
                ),
                Visibility(
                  visible: unRead,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Icon(Icons.circle, size: 6, color: themeData.red),
                  ),
                ),
              ],
            ),
            selected: category.id == selected?.id,
          );
        },
        itemCount: categories.length,
        separatorBuilder: (_, int index) => SizedBox(width: 8),
      ),
    );
  }
}
