import 'package:flutter/material.dart';
import 'package:vp_assets/cubit/assets/asset_summary_cubit.dart';
import 'package:vp_assets/generated/l10n.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
//import 'package:vp_assets/screen/asset_page/widgets/sub_account/assets_select_sub_acc.dart';
import 'package:vp_assets/widgets/eod_view.dart';
import 'package:vp_assets/widgets/selected_sub_account_widget.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'widgets/assets_statistics_body_center_v2.dart';
import 'widgets/assets_statistics_rtt_rate_v2.dart';
import 'widgets/nav/assets_statistics_display_money_v2.dart';

class AssetPage extends StatefulWidget {
  final SubAccountType? subAccountType;

  const AssetPage({super.key, this.subAccountType});

  @override
  State<AssetPage> createState() => _AssetPageState();
}

class _AssetPageState extends State<AssetPage>
    with AutomaticKeepAliveClientMixin, RouteAware {
  // final routeObserver = sl.get<AppPageRouteObserver>();

  late SubAccountType subAccountInitial =
      widget.subAccountType ?? SubAccountType.all;

  Widget _body() {
    return SafeArea(
      child: Column(
        children: [buildHeaderView(), Expanded(child: buildContentView())],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      body: SafeArea(child: _body()),
    );
  }

  Future<void> _showSubAccountBottomSheet(BuildContext context) async {
    final List<SubAccountModel> listSubAccounts = [subAccountTypeAll];
    listSubAccounts.addAll(
      GetIt.instance<SubAccountCubit>().subAccountsAllHaveDerivative,
    );
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    context.read<AssetSummaryCubit>().onSubAccountChanged(result);
  }

  Widget buildContentView() {
    return BlocConsumer<AssetSummaryCubit, AssetSummaryState>(
      listenWhen:
          (previous, current) =>
              previous.subAccount.id != current.subAccount.id,
      listener: (context, state) {
        //    context.read<AssetSummaryCubit>().fetchData();
      },

      builder: (_, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SelectedSubAccountWidget(subAccount: state.subAccount),
              //         /// nav amount
              buildNavAmountView(state.asssetSummaryModel),

              // chart
              const CashInButtonView(),
              AssetsStatisticsBodyCenterV2(
                totalMoney: state.asssetSummaryModel?.totalCash,
                totalHolding: state.asssetSummaryModel?.totalPortfolio,
                totalDebt: state.asssetSummaryModel?.totalDebt,
                // subAccountSelected: subAccountType,
              ),
              // StreamBuilder(
              //   stream: bloc.stockAmountChangedStream,
              //   builder: (_, __) {
              //     return AssetsStatisticsBodyCenterV2(
              //       hasError: state is AssetErrorState,
              //       totalDebt: bloc.getTotalAmountByType<DebtManager>(
              //         subAccountType: subAccountType,
              //       ),
              //       totalMoney: bloc.getTotalAmountByType<MoneyManager>(
              //         subAccountType: subAccountType,
              //       ),
              //       totalHolding: bloc.getTotalAmountByType<AssetHoldingManager>(
              //         subAccountType: subAccountType,
              //       ),
              //       subAccountSelected: subAccountType,
              //     );
              //   },
              // ),
              const SizedBox(height: 24),

              if (subAccountInitial == SubAccountType.all ||
                  subAccountInitial == SubAccountType.margin)
                AssetsStatisticsRttRateV2(
                  marginOverview: state.asssetSummaryModel?.margin,
                ),

              // if (subAccountType == SubAccountType.ps)
              //   AssetsStatisticDerivativeRttRate(
              //     derivativeAccountModel: _assetCubit.derivativeSummaryAccountModel,
              //   ),
              //       ],
              //     ),
              //     //     );
              //     //    },
              //   ),
              const Align(alignment: Alignment.bottomCenter, child: EODView()),
            ],
          ),
        );
      },
    );
  }

  Widget buildNavAmountView(AsssetSummaryModel? asssetSummary) {
    return AssetsStatisticsDisplayMoneyV2(
      profit: asssetSummary?.pnl,
      totalMoney: asssetSummary?.totalNav,
      profitPercent: asssetSummary?.percentPnL.getValuePercentAbs() ?? "-",
      subAccountSelected: subAccountInitial,
    );
  }

  Widget buildHeaderView() {
    return HeaderWidget(
      title: S.current.asset_overview,
      subTitle: S.current.asset_asset,
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class CashInButtonView extends StatelessWidget {
  const CashInButtonView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      child: Row(
        children: [
          /// nạp tiền
          VpsButton.primarySmall(
            title: S.current.asset_cash_in_money,
            onPressed: () {
              context.push('/moneyCashIn');
            },
          ),
          const SizedBox(width: 8),
          VpsButton.secondarySmall(
            title: S.current.asset_transfers,
            onPressed: () {
              context.push('/moneyTranfer');
            },
          ),
        ],
      ),
    );
  }
}
