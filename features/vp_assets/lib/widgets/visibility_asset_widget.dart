import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_assets/cubit/assets/asset_summary_cubit.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

class VisibilityAssetView extends StatelessWidget {
  const VisibilityAssetView({this.color, this.padding, super.key});

  final Color? color;

  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssetSummaryCubit, AssetSummaryState>(
      builder: (_, state) {
        final icon =
            state.isHidden
                ? Assets.icons.icOff.keyName
                : Assets.icons.icOn.keyName;

        double iconSize = state.isHidden ? 16 : 14;

        return InkResponse(
          onTap: () {
            context.read<AssetSummaryCubit>().updateHidden();
          },
          child: Padding(
            padding: padding ?? const EdgeInsets.only(left: 8, top: 4),
            child: SvgPicture.asset(
              icon,
              width: iconSize,
              height: iconSize,
              colorFilter: ColorFilter.mode(
                color ?? themeData.eyeColor,
                BlendMode.srcIn,
              ),
            ),
          ),
        );
      },
    );
  }
}

class VisibilityMoneyView extends StatelessWidget {
  const VisibilityMoneyView({
    required this.style,
    required this.value,
    this.useAutoSizeText = false,
    this.maxLines,
    this.maxFontSize,
    this.overflow,
    this.minFontSize = 12,
    this.textAlign,
    super.key,
  });

  final TextStyle style;

  final String? value;

  final int? maxLines;

  final double minFontSize;

  final double? maxFontSize;

  final TextOverflow? overflow;

  final bool useAutoSizeText;

  final TextAlign? textAlign;

  @override
  Widget build(BuildContext context) {
    if (value == null) {
      return Text(
        '--',
        style: style.copyWith(color: themeData.gray900),
        maxLines: maxLines,
        overflow: overflow,
      );
    }
    return BlocBuilder<AssetSummaryCubit, AssetSummaryState>(
      builder: (_, state) {
        return useAutoSizeText
            ? AutoSizeText(
              state.isHidden ? 0.toVisibilityOff() : value!,
              maxLines: maxLines,
              minFontSize: minFontSize,
              overflow: overflow,
              style: style,
              textAlign: textAlign,
            )
            : Text(
              state.isHidden ? 0.toVisibilityOff() : value!,
              style: style,
              maxLines: maxLines,
              overflow: overflow,
              textAlign: textAlign,
            );
      },
    );
  }
}
