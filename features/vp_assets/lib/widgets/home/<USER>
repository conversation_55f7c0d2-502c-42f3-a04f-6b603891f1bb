import 'package:flutter/material.dart';
import 'package:vp_assets/core/constant/argument.dart';
import 'package:vp_assets/cubit/assets/asset_summary_cubit.dart';
import 'package:vp_assets/generated/l10n.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_assets/router/asset_router.dart';
import 'package:vp_assets/widgets/visibility_asset_widget.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

final List<Color> _colors = [
  const Color(0xffAD1D26),
  const Color(0xffE5544F),
  const Color(0xffFEC200),
  const Color(0xff8FB93F),
  const Color(0xff20AF7D),
];

class AssetComponentView extends StatefulWidget {
  const AssetComponentView({super.key});

  @override
  AssetComponentViewState createState() => AssetComponentViewState();
}

class AssetComponentViewState extends State<AssetComponentView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 120 + 12),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: vpColor.backgroundElevation0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: BlocConsumer<AssetSummaryCubit, AssetSummaryState>(
                listenWhen:
                    (previous, current) =>
                        previous.subAccount.id != current.subAccount.id ||
                        previous.asssetSummaryModel?.totalAsset !=
                            current.asssetSummaryModel?.totalAsset,
                listener: (context, state) {
                  //    context.read<AssetSummaryCubit>().fetchData();
                },
                builder: (context, state) {
                  if (state.status == AssetSummaryStatus.loading) {
                    return const AssetLoadingView();
                  }
                  if (state.status == AssetSummaryStatus.success) {
                    return buildContentView(state.asssetSummaryModel!);
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),

            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget buildContentView(AsssetSummaryModel? asssetSummary) {
    final totalAsset = asssetSummary?.totalNav;
    final profit = asssetSummary?.pnl;
    final profitPercent = asssetSummary?.percentPnL;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        /// title
        InkWell(
          onTap: _navigateToMoney,
          child: Row(
            children: [
              Text(
                S.current.asset_property_nav,
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),

              /// icon show on/off money
              VisibilityAssetView(color: vpColor.iconPrimary),
              const Spacer(),
              Icon(
                Icons.arrow_forward_ios_sharp,
                color: themeData.white,
                size: 16,
              ),
            ],
          ),
        ),

        const SizedBox(height: 4),

        VisibilityMoneyView(
          value: totalAsset?.toMoney(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: vpTextStyle.headineBold6.copyColor(vpColor.textPrimary)!,
          useAutoSizeText: true,
        ),
        const SizedBox(height: 2),

        // profit
        VisibilityMoneyView(
          value:
              '${profit?.toMoney(addCharacter: true) ?? "-"} (${profitPercent?.getValuePercentAbs() ?? "-"})',
          style:
              vpTextStyle.subtitle14!.copyColor(
                CommonColorUtils.colorValue(profit),
              )!,
        ),
        const SizedBox(height: 16),
        _AssetsStatisticsRttRate(marginOverview: asssetSummary),
      ],
    );
  }

  void _navigateToMoney({int initialPage = 0}) {
    context.push(
      AssetRouter.assetOverview.routeName,
      extra: AssetOverviewPageArgument(initialPage: initialPage),
    );
  }
}

class AssetLoadingView extends StatelessWidget {
  const AssetLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// title
          Text(
            S.current.asset_property_nav,
            style: vpTextStyle.subtitle14.copyColor(themeData.gray700),
          ),
          const SizedBox(height: 8),

          buildLoadingView(context: context),

          const SizedBox(height: 8),

          Text(
            S.current.asset_actual_margin_ratio,
            style: vpTextStyle.subtitle16.copyColor(vpColor.textSecondary),
          ),
          const SizedBox(height: 8),

          buildLoadingView(context: context),
        ],
      ),
    );
  }

  Widget buildLoadingView({
    required BuildContext context,
    double? width,
    double? height,
  }) {
    return Shimmer.fromColors(
      baseColor: Color(context.isDarkMode ? 0xff21322A : 0xffcbeaf5),
      highlightColor: Color(context.isDarkMode ? 0xff1C2824 : 0xffe1f3e8),
      child: Container(
        height: height ?? 32,
        width: width ?? 240,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Color(context.isDarkMode ? 0xff21322A : 0xffcbeaf5),
        ),
      ),
    );
  }
}

class AssetErrorView extends StatelessWidget {
  const AssetErrorView({this.onRetry, Key? key}) : super(key: key);

  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          /// title
          Text(
            S.current.asset_property_nav,
            style: vpTextStyle.headineBold6.copyColor(themeData.gray700),
          ),

          const SizedBox(height: 8),
          ErrorView(
            error: VPCommonLocalize.current.error,
            buttonColor: themeData.primary,
            onTryAgain: () => onRetry?.call(),
          ),
        ],
      ),
    );
  }
}

class _AssetsStatisticsRttRate extends StatelessWidget {
  const _AssetsStatisticsRttRate({required this.marginOverview, Key? key})
    : super(key: key);

  final AsssetSummaryModel? marginOverview;
  num? get marginRate => marginOverview?.margin?.marginRate;
  // num? get marginRate => marginOverview?.marginrate;

  // num? get addvnd1 => marginOverview?.addvnd1;

  // num? get addvnd => marginOverview?.addvnd;

  Color getColor() {
    if (!isBorrowing) return themeData.gray900;

    if (marginRate! < 75) return themeData.redDark;

    if (marginRate! < 80) return themeData.red;

    if (marginRate! < 85) return themeData.yellow;

    if (marginRate! < 100) return themeData.gray500;

    return themeData.primary;
  }

  String get marginRateStr {
    if (marginRate! > 100) return '> 100%';

    return marginRate!.toDouble().getValuePercentAbs(
      removeFractionIfInteger: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (marginRate == null) {
      return const SizedBox(height: 0);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            /// Tỷ lệ ký quỹ thực tế (RTT)
            Expanded(
              child: Text(
                'Tỉ lệ ký quỹ thực tế',
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.iconSecondary,
                ),
              ),
            ),

            Text(
              isBorrowing ? marginRateStr : '--',
              style: vpTextStyle.subtitle16?.copyWith(
                color: getColor(),
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        const SizedBox(height: 32),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: LinearGradient(colors: _colors),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _Item(
                min: 0,
                max: 75,
                titlePositionLeft: 0,
                currentValue: marginRate!,
                title: 'Bán giải chấp',
              ),
              _Item(
                min: 75,
                max: 80,
                currentValue: marginRate!,
                titlePositionLeft: 50,
                title: S.current.asset_warning,
              ),
              _Item(min: 80, max: 85, currentValue: marginRate!),
              _Item(
                min: 85,
                max: 100,
                currentValue: marginRate!,
                title: S.current.asset_ensure,
              ),
              _Item(
                min: 100,
                max: double.infinity,
                currentValue: marginRate!,
                titlePositionRight: 0,
                title: S.current.asset_safe,
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool get isBorrowing => marginRate?.toInt() != 10000000;
}

class _Item extends StatelessWidget {
  final double min;

  final double max;

  final num currentValue;

  final String? title;

  final double? titlePositionLeft;

  final double? titlePositionRight;

  const _Item({
    Key? key,
    required this.min,
    required this.max,
    required this.currentValue,
    this.title,
    this.titlePositionLeft,
    this.titlePositionRight,
  }) : super(key: key);

  bool get isBorrowing => currentValue.toInt() != 10000000;

  bool get showToolTip =>
      currentValue >= min && currentValue < max && isBorrowing;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.topCenter,
        children: [
          const SizedBox(height: 4),
          if (showToolTip)
            Positioned(
              top: -6,
              child: Container(
                width: 5,
                height: 16,
                decoration: BoxDecoration(
                  border: Border.all(color: vpColor.strokeInfor),
                  borderRadius: BorderRadius.circular(3),
                  color: vpColor.backgroundElevation0,
                ),
              ),
            ),
          if (title != null)
            Positioned(
              top: -25,
              left: titlePositionLeft,
              right: titlePositionRight,
              child: Text(
                title!,
                style: vpTextStyle.captionMedium?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
