import 'package:vp_assets/core/repository/asset_repository.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_assets/model/asset_report/chart_data_report_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

part 'asset_summary_state.dart';

class AssetSummaryCubit extends Cubit<AssetSummaryState> {
  AssetSummaryCubit() : super(AssetSummaryState(subAccount: subAccountTypeAll));

  final AssetRepository _assetRepository = GetIt.instance<AssetRepository>();

  DateTime currentDate = DateTime.now();

  late DateTime fromDate = currentDate.firstDateOfMonth;

  late DateTime toDate = currentDate.lastDateOfMonth;

  void onSubAccountChanged(SubAccountModel subAccount) {
    emit(state.copyWith(subAccount: subAccount));
    fetchData(id: subAccount.id);
  }

  void onDateTimeAssetChange(int month) {
    currentDate = currentDate.copyWith(month: currentDate.month + month);

    fromDate = currentDate.firstDateOfMonth;

    toDate = currentDate.lastDateOfMonth;

    getInvestmentAssets(
      id: (state.subAccount.isTypeAll ? "" : state.subAccount.id),
      fromDate: fromDate.toDateRequestInvestmentAssets(),
      toDate: toDate.toDateRequestInvestmentAssets(),
    );
  }

  Future<void> onRefresh() async {
    fetchData();
  }

  void fetchData({String? id}) async {
    emit(state.copyWith(status: AssetSummaryStatus.loading));

    try {
      final result = await _assetRepository.getSummaryAccounts(
        id ?? (state.subAccount.isTypeAll ? "" : state.subAccount.id),
      );
      emit(
        state.copyWith(
          status: AssetSummaryStatus.success,
          asssetSummaryModel: result,
        ),
      );
      getInvestmentAssets(
        id: id,
        fromDate: fromDate.toDateRequestInvestmentAssets(),
        toDate: toDate.toDateRequestInvestmentAssets(),
      );
    } catch (e, _) {
      emit(state.copyWith(status: AssetSummaryStatus.failure));
    }
  }

  void getInvestmentAssets({
    String? id,
    String? fromDate,
    String? toDate,
  }) async {
    emit(state.copyWith(status: AssetSummaryStatus.loading));

    try {
      final result = await _assetRepository.getInvestmentAssets(
        accountId: getTypeSubAccount(
          GetIt.instance<SubAccountCubit>().subAccountType,
        ),
        fromDate: fromDate,
        toDate: toDate,
      );
      if (result != null) {
        final listChartAsset =
            result
                .map(
                  (e) => ChartAssetReportModel(
                    e.date ?? '',
                    (e.nav ?? 0).toDouble(),
                    y1: e.debt ?? 0,
                  ),
                )
                .toList();
        emit(
          state.copyWith(
            status: AssetSummaryStatus.success,
            investmentGrowth: listChartAsset,
          ),
        );
      } else {
        emit(state.copyWith(status: AssetSummaryStatus.success));
      }
    } catch (e, _) {
      emit(state.copyWith(status: AssetSummaryStatus.failure));
    }
  }

  String getTypeSubAccount(SubAccountType subAccountType) {
    switch (subAccountType) {
      case SubAccountType.all:
        return '0';
      case SubAccountType.normal:
        return '1';
      case SubAccountType.margin:
        return '2';
      case SubAccountType.bond:
        return '4';
      case SubAccountType.derivative:
        return '5';
      case SubAccountType.commission:
      case SubAccountType.wealth:
        return '0';
    }
  }

  void updateHidden() {
    emit(state.copyWith(isHidden: !state.isHidden));
  }
}
