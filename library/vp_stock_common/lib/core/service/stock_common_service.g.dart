// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_common_service.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations,unused_element_parameter

class _StockCommonService implements StockCommonService {
  _StockCommonService(this._dio, {this.baseUrl, this.errorLogger});

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<BaseResponse<bool>> getEODStatus() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<bool>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/condition-order/common/signal-eod',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<bool> _value;
    try {
      _value = BaseResponse<bool>.fromJson(
        _result.data!,
        (json) => json as bool,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<StockInfoBySymbolsModel>> getStockInfoBySymbols(
    String indexCode,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'symbols': indexCode};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<StockInfoBySymbolsModel>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/invest/api/stockInfoByList',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<StockInfoBySymbolsModel> _value;
    try {
      _value = BaseResponse<StockInfoBySymbolsModel>.fromJson(
        _result.data!,
        (json) =>
            StockInfoBySymbolsModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<BasePagingResponse<StockModel>?>> getStockList({
    String? marketCode,
    int? pageNo,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'marketCode': marketCode,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
      BaseResponse<BasePagingResponse<StockModel>?>
    >(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockList',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<BasePagingResponse<StockModel>?> _value;
    try {
      _value = BaseResponse<BasePagingResponse<StockModel>?>.fromJson(
        _result.data!,
        (json) => BasePagingResponse<StockModel>.fromJson(
          json as Map<String, dynamic>,
          (json) => StockModel.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>> getStockInfoV2({
    String? marketCode,
    String? symbols,
    String? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'marketCode': marketCode,
      r'symbols': symbols,
      r'stockType': stockType,
      r'issuerName': issuerName,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
      BaseResponse<BasePagingResponse<StockInfoModel>>
    >(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/noauth/public/v1/stock/stockDetail',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<BasePagingResponse<StockInfoModel>> _value;
    try {
      _value = BaseResponse<BasePagingResponse<StockInfoModel>>.fromJson(
        _result.data!,
        (json) => BasePagingResponse<StockInfoModel>.fromJson(
          json as Map<String, dynamic>,
          (json) => StockInfoModel.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<QuoteModel>>> quote({String? symbols}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'symbols': symbols};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<List<QuoteModel>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/quote',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<QuoteModel>> _value;
    try {
      _value = BaseResponse<List<QuoteModel>>.fromJson(
        _result.data!,
        (json) =>
            json is List<dynamic>
                ? json
                    .map<QuoteModel>(
                      (i) => QuoteModel.fromJson(i as Map<String, dynamic>),
                    )
                    .toList()
                : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<FuQuoteModel>>> fuQuote({String? symbols}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'symbols': symbols};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<List<FuQuoteModel>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/fuQuote',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<FuQuoteModel>> _value;
    try {
      _value = BaseResponse<List<FuQuoteModel>>.fromJson(
        _result.data!,
        (json) =>
            json is List<dynamic>
                ? json
                    .map<FuQuoteModel>(
                      (i) => FuQuoteModel.fromJson(i as Map<String, dynamic>),
                    )
                    .toList()
                : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>> getFuStockDetail({
    String? symbols,
    String? stockType,
    int? pageNo,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'symbols': symbols,
      r'stockType': stockType,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
      BaseResponse<BasePagingResponse<StockInfoModel>>
    >(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/fuStockDetail',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<BasePagingResponse<StockInfoModel>> _value;
    try {
      _value = BaseResponse<BasePagingResponse<StockInfoModel>>.fromJson(
        _result.data!,
        (json) => BasePagingResponse<StockInfoModel>.fromJson(
          json as Map<String, dynamic>,
          (json) => StockInfoModel.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>>
  getOddLotStockDetail({
    String? marketCode,
    String? symbols,
    String? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'marketCode': marketCode,
      r'symbols': symbols,
      r'stockType': stockType,
      r'issuerName': issuerName,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
      BaseResponse<BasePagingResponse<StockInfoModel>>
    >(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/oddLotStockDetail',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<BasePagingResponse<StockInfoModel>> _value;
    try {
      _value = BaseResponse<BasePagingResponse<StockInfoModel>>.fromJson(
        _result.data!,
        (json) => BasePagingResponse<StockInfoModel>.fromJson(
          json as Map<String, dynamic>,
          (json) => StockInfoModel.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<WatchlistModel>>> getSuggestedLists() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<List<WatchlistModel>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/suggestedLists',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<WatchlistModel>> _value;
    try {
      _value = BaseResponse<List<WatchlistModel>>.fromJson(
        _result.data!,
        (json) =>
            json is List<dynamic>
                ? json
                    .map<WatchlistModel>(
                      (i) => WatchlistModel.fromJson(i as Map<String, dynamic>),
                    )
                    .toList()
                : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>> getStocksByIndex({
    String? indexCode,
    int? pageNo,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'indexCode': indexCode,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
      BaseResponse<BasePagingResponse<StockInfoModel>>
    >(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockDetailByIndex',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<BasePagingResponse<StockInfoModel>> _value;
    try {
      _value = BaseResponse<BasePagingResponse<StockInfoModel>>.fromJson(
        _result.data!,
        (json) => BasePagingResponse<StockInfoModel>.fromJson(
          json as Map<String, dynamic>,
          (json) => StockInfoModel.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<MarketInfoModel>> getMarketInfo(String indexCode) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'indexCode': indexCode};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<MarketInfoModel>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/market/intradayMarketIndex',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<MarketInfoModel> _value;
    try {
      _value = BaseResponse<MarketInfoModel>.fromJson(
        _result.data!,
        (json) => MarketInfoModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<BondModel>>> getCorpBondList({String? type}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'type': type};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<List<BondModel>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/corpBondList',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<BondModel>> _value;
    try {
      _value = BaseResponse<List<BondModel>>.fromJson(
        _result.data!,
        (json) =>
            json is List<dynamic>
                ? json
                    .map<BondModel>(
                      (i) => BondModel.fromJson(i as Map<String, dynamic>),
                    )
                    .toList()
                : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<FuTop10PriceModel>>> getFuTop10Price({
    required String symbol,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'symbol': symbol};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<List<FuTop10PriceModel>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/fuTop10Price',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<FuTop10PriceModel>> _value;
    try {
      _value = BaseResponse<List<FuTop10PriceModel>>.fromJson(
        _result.data!,
        (json) =>
            json is List<dynamic>
                ? json
                    .map<FuTop10PriceModel>(
                      (i) =>
                          FuTop10PriceModel.fromJson(i as Map<String, dynamic>),
                    )
                    .toList()
                : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<PositionsModel>> getOpenPositions({
    required String symbol,
    required String accountId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'symbol': symbol,
      r'accountId': accountId,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<PositionsModel>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/portfolio/derivatives/positions',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<PositionsModel> _value;
    try {
      _value = BaseResponse<PositionsModel>.fromJson(
        _result.data!,
        (json) => PositionsModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<CurrentTimeModel>> getCurrentTime() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<CurrentTimeModel>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/time/systemTime',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<CurrentTimeModel> _value;
    try {
      _value = BaseResponse<CurrentTimeModel>.fromJson(
        _result.data!,
        (json) => CurrentTimeModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus({
    String? marketCode,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'marketCode': marketCode};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<List<MarketStatusModel>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/noauth/public/v1/market/marketStatus',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<MarketStatusModel>> _value;
    try {
      _value = BaseResponse<List<MarketStatusModel>>.fromJson(
        _result.data!,
        (json) =>
            json is List<dynamic>
                ? json
                    .map<MarketStatusModel>(
                      (i) =>
                          MarketStatusModel.fromJson(i as Map<String, dynamic>),
                    )
                    .toList()
                : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
